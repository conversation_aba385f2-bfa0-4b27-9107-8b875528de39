"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/constants/addresses.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/providers/provider-browser.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/address/checks.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/contract/contract.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/utils/units.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../config */ \"(app-pages-browser)/./src/config.ts\");\n/* harmony import */ var _contracts_SecurityTokenFactory_json__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../contracts/SecurityTokenFactory.json */ \"(app-pages-browser)/./src/contracts/SecurityTokenFactory.json\");\n/* harmony import */ var _contracts_SecurityToken_json__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../contracts/SecurityToken.json */ \"(app-pages-browser)/./src/contracts/SecurityToken.json\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction Home() {\n    _s();\n    const [tokens, setTokens] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [network, setNetwork] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('amoy');\n    const [factoryAddress, setFactoryAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [tokenImplementation, setTokenImplementation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [whitelistImplementation, setWhitelistImplementation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [verificationAddress, setVerificationAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [walletConnected, setWalletConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loadMethod, setLoadMethod] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('logs');\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            // Get contract addresses for current network\n            const addresses = (0,_config__WEBPACK_IMPORTED_MODULE_3__.getContractAddresses)(network);\n            setFactoryAddress(addresses.factory || '');\n            setTokenImplementation(addresses.tokenImplementation || '');\n            setWhitelistImplementation(addresses.whitelistImplementation || '');\n            checkWalletConnection();\n        }\n    }[\"Home.useEffect\"], [\n        network\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            if (walletConnected) {\n                fetchTokens();\n            } else {\n                // Load known tokens even without wallet connection for display\n                loadKnownTokensOnly();\n            }\n        }\n    }[\"Home.useEffect\"], [\n        walletConnected,\n        network\n    ]);\n    // Function to load only known tokens (for display without wallet)\n    const loadKnownTokensOnly = async ()=>{\n        setIsLoading(true);\n        setError(null);\n        try {\n            const knownTokens = (0,_config__WEBPACK_IMPORTED_MODULE_3__.getKnownTokens)(network);\n            if (knownTokens.length > 0) {\n                console.log(\"Loading \".concat(knownTokens.length, \" known tokens for display...\"));\n                // Create basic token objects from known tokens\n                const basicTokens = knownTokens.map((knownToken)=>({\n                        address: knownToken.address,\n                        name: knownToken.name,\n                        symbol: knownToken.symbol,\n                        maxSupply: \"Connect wallet to view\",\n                        totalSupply: \"Connect wallet to view\",\n                        whitelistAddress: ethers__WEBPACK_IMPORTED_MODULE_6__.ZeroAddress,\n                        tokenImageUrl: \"\"\n                    }));\n                setTokens(basicTokens);\n                setError(\"Connect your wallet to view live token data and access management features.\");\n            } else {\n                setTokens([]);\n                setError(\"No known tokens configured for this network. Connect your wallet to discover tokens from the factory.\");\n            }\n        } catch (error) {\n            console.error(\"Error loading known tokens:\", error);\n            setError(\"Error loading token information. Please try again.\");\n        }\n        setIsLoading(false);\n    };\n    const checkWalletConnection = async ()=>{\n        try {\n            if (window.ethereum) {\n                const provider = new ethers__WEBPACK_IMPORTED_MODULE_7__.BrowserProvider(window.ethereum);\n                const accounts = await provider.listAccounts();\n                if (accounts.length > 0) {\n                    setWalletConnected(true);\n                } else {\n                    setWalletConnected(false);\n                }\n            }\n        } catch (error) {\n            console.error(\"Error checking wallet connection:\", error);\n            setWalletConnected(false);\n        }\n    };\n    const connectWallet = async ()=>{\n        try {\n            if (window.ethereum) {\n                const provider = new ethers__WEBPACK_IMPORTED_MODULE_7__.BrowserProvider(window.ethereum);\n                await provider.send(\"eth_requestAccounts\", []);\n                setWalletConnected(true);\n            } else {\n                setError(\"Please install MetaMask to use this feature!\");\n            }\n        } catch (error) {\n            console.error(\"Error connecting wallet:\", error);\n        }\n    };\n    // Add your token address to verify\n    const addTokenManually = ()=>{\n        if (!verificationAddress) {\n            alert('Please enter a token address');\n            return;\n        }\n        if (!ethers__WEBPACK_IMPORTED_MODULE_8__.isAddress(verificationAddress)) {\n            alert('Please enter a valid address');\n            return;\n        }\n        loadTokenByAddress(verificationAddress);\n    };\n    const loadTokenByAddress = async (address)=>{\n        try {\n            setIsLoading(true);\n            if (!window.ethereum) {\n                throw new Error('Please install MetaMask to use this feature!');\n            }\n            // Use the new loadTokenDetails function\n            const tokenDetails = await loadTokenDetails(address);\n            if (tokenDetails) {\n                // Check if the token is already in the list\n                const exists = tokens.some((token)=>token.address.toLowerCase() === address.toLowerCase());\n                if (!exists) {\n                    setTokens((prevTokens)=>[\n                            ...prevTokens,\n                            tokenDetails\n                        ]);\n                }\n                setVerificationAddress('');\n            } else {\n                alert(\"Could not load token details. Is this a valid Security Token address?\");\n            }\n            setIsLoading(false);\n        } catch (err) {\n            console.error('Error loading token:', err);\n            setError(err.message || 'Error loading token. Please try again.');\n            setIsLoading(false);\n        }\n    };\n    const loadTokenDetails = async (address)=>{\n        try {\n            if (!window.ethereum) {\n                throw new Error('MetaMask not available');\n            }\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_7__.BrowserProvider(window.ethereum);\n            // Get the token contract\n            const tokenContract = new ethers__WEBPACK_IMPORTED_MODULE_9__.Contract(address, _contracts_SecurityToken_json__WEBPACK_IMPORTED_MODULE_5__.abi, provider);\n            // Read token details\n            const name = await tokenContract.name();\n            const symbol = await tokenContract.symbol();\n            // Get decimals to format amounts correctly\n            const decimalsRaw = await tokenContract.decimals();\n            const decimals = Number(decimalsRaw);\n            let maxSupply = \"0\";\n            let totalSupply = \"0\";\n            let whitelistAddress = \"******************************************\";\n            try {\n                const maxSupplyRaw = await tokenContract.maxSupply();\n                maxSupply = decimals === 0 ? maxSupplyRaw.toString() : ethers__WEBPACK_IMPORTED_MODULE_10__.formatUnits(maxSupplyRaw, decimals);\n            } catch (err) {\n                console.warn(\"Could not read maxSupply:\", err);\n            }\n            try {\n                const totalSupplyRaw = await tokenContract.totalSupply();\n                totalSupply = decimals === 0 ? totalSupplyRaw.toString() : ethers__WEBPACK_IMPORTED_MODULE_10__.formatUnits(totalSupplyRaw, decimals);\n            } catch (err) {\n                console.warn(\"Could not read totalSupply:\", err);\n            }\n            try {\n                whitelistAddress = await tokenContract.identityRegistry();\n            } catch (err) {\n                try {\n                    whitelistAddress = await tokenContract.whitelistAddress();\n                } catch (err2) {\n                    console.warn(\"Could not read whitelist address:\", err2);\n                }\n            }\n            // Try to get token image URL if supported\n            let tokenImageUrl = \"\";\n            try {\n                tokenImageUrl = await tokenContract.tokenImageUrl();\n            } catch (err) {\n                console.log(\"Token doesn't support image URL or image URL is empty\");\n            }\n            return {\n                address,\n                name,\n                symbol,\n                maxSupply,\n                totalSupply,\n                whitelistAddress,\n                tokenImageUrl\n            };\n        } catch (error) {\n            console.error(\"Error loading token details for \".concat(address, \":\"), error);\n            return null;\n        }\n    };\n    const fetchTokens = async ()=>{\n        setIsLoading(true);\n        setError(null);\n        setTokens([]);\n        try {\n            if (!window.ethereum) {\n                throw new Error('Please install MetaMask to use this feature!');\n            }\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_7__.BrowserProvider(window.ethereum);\n            const networkConfig = (0,_config__WEBPACK_IMPORTED_MODULE_3__.getNetworkConfig)(network);\n            const contractAddresses = (0,_config__WEBPACK_IMPORTED_MODULE_3__.getContractAddresses)(network);\n            if (!contractAddresses.factory) {\n                throw new Error(\"No factory address configured for network: \".concat(network));\n            }\n            // Connect to the factory contract\n            const factory = new ethers__WEBPACK_IMPORTED_MODULE_9__.Contract(contractAddresses.factory, _contracts_SecurityTokenFactory_json__WEBPACK_IMPORTED_MODULE_4__.abi, provider);\n            console.log(\"Loading tokens from factory:\", contractAddresses.factory);\n            console.log(\"Network:\", network);\n            console.log(\"Provider:\", provider);\n            try {\n                // Try to get tokens using the new enumeration methods\n                console.log(\"Attempting to fetch tokens using factory enumeration...\");\n                try {\n                    // First, try to get the token count (new factory enumeration)\n                    const tokenCount = await factory.getTokenCount();\n                    console.log(\"Factory reports \".concat(tokenCount, \" deployed tokens\"));\n                    if (tokenCount > 0) {\n                        // Get all token addresses at once for better performance\n                        const tokenAddresses = await factory.getAllDeployedTokens();\n                        console.log(\"Retrieved token addresses:\", tokenAddresses);\n                        // Load details for each token\n                        const tokenPromises = tokenAddresses.map(async (address)=>{\n                            try {\n                                return await loadTokenDetails(address);\n                            } catch (error) {\n                                console.warn(\"Failed to load token details for \".concat(address, \":\"), error);\n                                return null;\n                            }\n                        });\n                        const tokenResults = await Promise.all(tokenPromises);\n                        const validTokens = tokenResults.filter((token)=>token !== null);\n                        if (validTokens.length > 0) {\n                            setTokens(validTokens);\n                            console.log(\"Successfully loaded \".concat(validTokens.length, \" tokens from factory\"));\n                        } else {\n                            setError(\"Factory has tokens but could not load their details. Please check network connection.\");\n                        }\n                    } else {\n                        setError(\"No tokens found in factory. Create your first token to see it here.\");\n                    }\n                } catch (enumerationError) {\n                    console.warn(\"Factory enumeration failed, trying event-based discovery:\", enumerationError);\n                    // Fallback: Use event-based token discovery for older factories\n                    try {\n                        console.log(\"Searching for TokenDeployed events...\");\n                        // Get TokenDeployed events from the factory\n                        const filter = factory.filters.TokenDeployed();\n                        let events = [];\n                        // Try different block ranges to find events\n                        const ranges = [\n                            -10000,\n                            -50000\n                        ]; // Last 10k, then 50k blocks\n                        for (const range of ranges){\n                            try {\n                                events = await factory.queryFilter(filter, range);\n                                if (events.length > 0) break; // Found events, stop searching\n                            } catch (error) {\n                                console.warn(\"Failed to query \".concat(Math.abs(range), \" blocks:\"), error);\n                            }\n                        }\n                        console.log(\"Found \".concat(events.length, \" TokenDeployed events\"));\n                        if (events.length > 0) {\n                            // Extract unique token addresses from events\n                            const tokenAddresses = [\n                                ...new Set(events.map((event)=>{\n                                    var _event_args;\n                                    return (_event_args = event.args) === null || _event_args === void 0 ? void 0 : _event_args.tokenAddress;\n                                }).filter(Boolean))\n                            ];\n                            console.log(\"Token addresses from events:\", tokenAddresses);\n                            // Load details for each token\n                            const tokenPromises = tokenAddresses.map(async (address)=>{\n                                try {\n                                    return await loadTokenDetails(address);\n                                } catch (error) {\n                                    console.warn(\"Failed to load token details for \".concat(address, \":\"), error);\n                                    return null;\n                                }\n                            });\n                            const tokenResults = await Promise.all(tokenPromises);\n                            const validTokens = tokenResults.filter((token)=>token !== null);\n                            if (validTokens.length > 0) {\n                                setTokens(validTokens);\n                                console.log(\"Successfully loaded \".concat(validTokens.length, \" tokens from events\"));\n                            } else {\n                                setError(\"Found token events but could not load token details. Please check network connection.\");\n                            }\n                        } else {\n                            setError(\"No tokens found in factory events. Create your first token to see it here.\");\n                        }\n                    } catch (eventError) {\n                        console.warn(\"Event-based discovery failed:\", eventError);\n                        // Final fallback: Load known tokens from configuration\n                        console.log(\"Falling back to known tokens from configuration...\");\n                        const knownTokens = (0,_config__WEBPACK_IMPORTED_MODULE_3__.getKnownTokens)(network);\n                        if (knownTokens.length > 0) {\n                            console.log(\"Loading \".concat(knownTokens.length, \" known tokens...\"));\n                            const knownTokenPromises = knownTokens.map(async (knownToken)=>{\n                                try {\n                                    const tokenDetails = await loadTokenDetails(knownToken.address);\n                                    return tokenDetails;\n                                } catch (error) {\n                                    console.warn(\"Failed to load known token \".concat(knownToken.address, \":\"), error);\n                                    // Return basic info even if contract call fails\n                                    return {\n                                        address: knownToken.address,\n                                        name: knownToken.name,\n                                        symbol: knownToken.symbol,\n                                        maxSupply: \"Unknown\",\n                                        totalSupply: \"Unknown\",\n                                        whitelistAddress: ethers__WEBPACK_IMPORTED_MODULE_6__.ZeroAddress,\n                                        tokenImageUrl: \"\"\n                                    };\n                                }\n                            });\n                            const knownTokenResults = await Promise.all(knownTokenPromises);\n                            const validKnownTokens = knownTokenResults.filter((token)=>token !== null);\n                            if (validKnownTokens.length > 0) {\n                                setTokens(validKnownTokens);\n                                console.log(\"Successfully loaded \".concat(validKnownTokens.length, \" known tokens\"));\n                                setError(\"Factory enumeration failed, but loaded known tokens. You can add more tokens manually below.\");\n                            } else {\n                                setError(\"Could not load tokens from factory or known tokens. Please add token addresses manually using the tool below.\");\n                            }\n                        } else {\n                            setError(\"Could not load tokens from factory. Please add token addresses manually using the tool below.\");\n                        }\n                    }\n                }\n            } catch (err) {\n                console.error(\"Error loading tokens:\", err);\n                const errorMessage = err instanceof Error ? err.message : \"Unknown error\";\n                // Try to load known tokens as fallback even on factory connection error\n                console.log(\"Factory connection failed, trying known tokens...\");\n                const knownTokens = (0,_config__WEBPACK_IMPORTED_MODULE_3__.getKnownTokens)(network);\n                if (knownTokens.length > 0) {\n                    try {\n                        const knownTokenPromises = knownTokens.map(async (knownToken)=>{\n                            try {\n                                const tokenDetails = await loadTokenDetails(knownToken.address);\n                                return tokenDetails;\n                            } catch (error) {\n                                console.warn(\"Failed to load known token \".concat(knownToken.address, \":\"), error);\n                                return {\n                                    address: knownToken.address,\n                                    name: knownToken.name,\n                                    symbol: knownToken.symbol,\n                                    maxSupply: \"Unknown\",\n                                    totalSupply: \"Unknown\",\n                                    whitelistAddress: ethers__WEBPACK_IMPORTED_MODULE_6__.ZeroAddress,\n                                    tokenImageUrl: \"\"\n                                };\n                            }\n                        });\n                        const knownTokenResults = await Promise.all(knownTokenPromises);\n                        const validKnownTokens = knownTokenResults.filter((token)=>token !== null);\n                        if (validKnownTokens.length > 0) {\n                            setTokens(validKnownTokens);\n                            console.log(\"Loaded \".concat(validKnownTokens.length, \" known tokens as fallback\"));\n                            setError(\"Factory connection failed, but loaded known tokens. Please check your network connection.\");\n                        } else {\n                            setError(\"Could not load tokens from factory or known tokens. Please add token addresses manually using the verify tool below.\");\n                        }\n                    } catch (fallbackError) {\n                        console.error(\"Fallback token loading failed:\", fallbackError);\n                        setError(\"Could not load tokens from factory or known tokens. Please add token addresses manually using the verify tool below.\");\n                    }\n                } else {\n                    setError(\"Could not load tokens from factory. Please add token addresses manually using the verify tool below.\");\n                }\n            }\n            setIsLoading(false);\n        } catch (err) {\n            console.error('Error fetching tokens:', err);\n            setError(err.message || 'Error fetching tokens. Please try again.');\n            setIsLoading(false);\n        }\n    };\n    const handleNetworkChange = (event)=>{\n        const newNetwork = event.target.value;\n        console.log(\"Switching to network: \".concat(newNetwork));\n        setNetwork(newNetwork);\n    };\n    const verifyToken = ()=>{\n        if (!verificationAddress) {\n            alert('Please enter a token address to verify');\n            return;\n        }\n        let explorerUrl = '';\n        if (network === 'amoy') {\n            explorerUrl = \"https://www.oklink.com/amoy/address/\".concat(verificationAddress);\n        } else if (network === 'polygon') {\n            explorerUrl = \"https://polygonscan.com/address/\".concat(verificationAddress);\n        }\n        if (explorerUrl) {\n            window.open(explorerUrl, '_blank');\n        }\n    };\n    const getBlockExplorerUrl = (network)=>{\n        if (network === 'amoy') {\n            return 'https://www.oklink.com/amoy';\n        } else if (network === 'polygon') {\n            return 'https://polygonscan.com';\n        }\n        return '#';\n    };\n    const viewFactoryTokens = ()=>{\n        let explorerUrl = '';\n        if (network === 'amoy') {\n            explorerUrl = \"https://www.oklink.com/amoy/address/\".concat(factoryAddress, \"#eventlog\");\n        } else if (network === 'polygon') {\n            explorerUrl = \"https://polygonscan.com/address/\".concat(factoryAddress, \"#events\");\n        }\n        if (explorerUrl) {\n            window.open(explorerUrl, '_blank');\n        }\n    };\n    const formatNumber = (value)=>{\n        // Format the number with commas\n        const number = parseFloat(value);\n        return number.toLocaleString(undefined, {\n            maximumFractionDigits: 0\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold mb-2\",\n                        children: \"Security Token Dashboard\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 517,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"View and manage your security tokens\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 518,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 516,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 flex justify-between items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"network\",\n                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                children: \"Network\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 525,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                id: \"network\",\n                                className: \"p-2 border border-gray-300 rounded-md\",\n                                value: network,\n                                onChange: handleNetworkChange,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"amoy\",\n                                        children: \"Amoy Testnet\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 534,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"polygon\",\n                                        children: \"Polygon Mainnet\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 535,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 528,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 524,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-3\",\n                        children: [\n                            !walletConnected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: connectWallet,\n                                className: \"bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded transition\",\n                                children: \"Connect Wallet\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 541,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/modular-tokens\",\n                                className: \"bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded transition\",\n                                children: \"Create New Token\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 548,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 539,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 523,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: error\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 559,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 558,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-green-50 border-l-4 border-green-500 text-green-700 p-4 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"font-bold mb-2\",\n                        children: \"Connected to Your Deployed Factory Contract\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 564,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4 mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm font-semibold\",\n                                        children: \"Factory Address:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 568,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"font-mono text-sm\",\n                                        children: factoryAddress\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 569,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 567,\n                                columnNumber: 11\n                            }, this),\n                            tokenImplementation && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm font-semibold\",\n                                        children: \"Token Implementation:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 574,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"font-mono text-sm\",\n                                        children: tokenImplementation\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 575,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 573,\n                                columnNumber: 13\n                            }, this),\n                            whitelistImplementation && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm font-semibold\",\n                                        children: \"Whitelist Implementation:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 581,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"font-mono text-sm\",\n                                        children: whitelistImplementation\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 582,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 580,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 566,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4 mt-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"\".concat(getBlockExplorerUrl(network), \"/address/\").concat(factoryAddress),\n                                target: \"_blank\",\n                                rel: \"noopener noreferrer\",\n                                className: \"text-green-700 underline hover:text-green-900\",\n                                children: [\n                                    \"View Factory on \",\n                                    network === 'amoy' ? 'OKLink Explorer' : 'PolygonScan'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 588,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: viewFactoryTokens,\n                                className: \"bg-green-600 hover:bg-green-700 text-white py-1 px-3 rounded text-sm\",\n                                children: \"View All Factory Token Deployments\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 597,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 587,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 563,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white shadow-md rounded-lg p-6 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-xl font-semibold mb-4\",\n                        children: \"Quick Actions\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 607,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap gap-3 mb-6 p-4 bg-gray-50 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/modular-tokens\",\n                                className: \"bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded text-sm font-medium\",\n                                children: \"\\uD83D\\uDE80 Create Token\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 611,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/transfer-controls\",\n                                className: \"bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded text-sm font-medium\",\n                                children: \"\\uD83D\\uDD12 Transfer Controls\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 617,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/clients\",\n                                className: \"bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded text-sm font-medium\",\n                                children: \"\\uD83D\\uDC65 Client Management\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 623,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>window.open('https://amoy.polygonscan.com/', '_blank'),\n                                className: \"bg-gray-600 hover:bg-gray-700 text-white py-2 px-4 rounded text-sm font-medium\",\n                                children: \"\\uD83D\\uDD0D Block Explorer\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 629,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 610,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-lg font-medium mb-2\",\n                        children: \"Add Token to Dashboard\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 637,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mb-4 text-gray-600\",\n                        children: \"Manually add a token by entering its address below.\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 638,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                value: verificationAddress,\n                                onChange: (e)=>setVerificationAddress(e.target.value),\n                                placeholder: \"Enter token address (0x...)\",\n                                className: \"flex-grow p-2 border border-gray-300 rounded\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 642,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: addTokenManually,\n                                className: \"bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded\",\n                                children: \"Add Token\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 649,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: verifyToken,\n                                className: \"bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded\",\n                                children: \"Verify on Explorer\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 655,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 641,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-2 text-xs text-gray-500\",\n                        children: \"Note: After creating a token, you can add it here to display it on the dashboard.\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 662,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 606,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white shadow-md rounded-lg overflow-hidden mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 border-b\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-start\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-semibold\",\n                                                children: \"Security Token Dashboard\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 671,\n                                                columnNumber: 15\n                                            }, this),\n                                            walletConnected ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600 mt-1\",\n                                                children: [\n                                                    \"Live data from \",\n                                                    network === 'amoy' ? 'Amoy Testnet' : 'Polygon Mainnet',\n                                                    \" factory\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 673,\n                                                columnNumber: 17\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600 mt-1\",\n                                                children: \"Showing known tokens - connect wallet for live data\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 677,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 670,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-3 h-3 rounded-full \".concat(walletConnected ? 'bg-green-500' : 'bg-yellow-500')\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 683,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: walletConnected ? 'Connected' : 'Offline Mode'\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 684,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 682,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 669,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-2 mt-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: walletConnected ? fetchTokens : loadKnownTokensOnly,\n                                        className: \"text-sm bg-blue-500 hover:bg-blue-600 text-white py-1 px-2 rounded\",\n                                        disabled: isLoading,\n                                        children: isLoading ? 'Loading...' : 'Refresh Token List'\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 691,\n                                        columnNumber: 13\n                                    }, this),\n                                    !walletConnected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: connectWallet,\n                                        className: \"text-sm bg-green-500 hover:bg-green-600 text-white py-1 px-2 rounded\",\n                                        children: \"Connect for Live Data\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 699,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 690,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 668,\n                        columnNumber: 9\n                    }, this),\n                    isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center my-12\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 711,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 710,\n                        columnNumber: 11\n                    }, this) : tokens.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                        className: \"min-w-full divide-y divide-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                className: \"bg-gray-50\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"Name\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 717,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"Symbol\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 720,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"Total Supply\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 723,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"Max Supply\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 726,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"Source\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 729,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"Actions\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 732,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 716,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 715,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                className: \"bg-white divide-y divide-gray-200\",\n                                children: tokens.map((token, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        className: \"hover:bg-gray-50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-shrink-0 h-10 w-10\",\n                                                            children: [\n                                                                token.tokenImageUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                    className: \"h-10 w-10 rounded-full object-cover border-2 border-gray-200\",\n                                                                    src: token.tokenImageUrl,\n                                                                    alt: \"\".concat(token.name, \" logo\"),\n                                                                    onError: (e)=>{\n                                                                        var _target_nextElementSibling;\n                                                                        // Fallback to default icon if image fails to load\n                                                                        const target = e.target;\n                                                                        target.style.display = 'none';\n                                                                        (_target_nextElementSibling = target.nextElementSibling) === null || _target_nextElementSibling === void 0 ? void 0 : _target_nextElementSibling.classList.remove('hidden');\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 745,\n                                                                    columnNumber: 27\n                                                                }, this) : null,\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center \".concat(token.tokenImageUrl ? 'hidden' : ''),\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium text-gray-700\",\n                                                                        children: token.symbol.substring(0, 2).toUpperCase()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 759,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 758,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 743,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"ml-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm font-medium text-gray-900\",\n                                                                    children: token.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 765,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-gray-500 truncate\",\n                                                                    title: token.address,\n                                                                    children: [\n                                                                        token.address.substring(0, 10),\n                                                                        \"...\",\n                                                                        token.address.substring(token.address.length - 8)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 766,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 764,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 741,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 740,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                children: token.symbol\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 772,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                children: token.totalSupply ? formatNumber(token.totalSupply) : \"—\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 775,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                children: token.maxSupply ? formatNumber(token.maxSupply) : \"—\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 778,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \".concat(walletConnected && token.totalSupply !== \"Connect wallet to view\" ? (0,_config__WEBPACK_IMPORTED_MODULE_3__.isKnownToken)(network, token.address) ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'),\n                                                    children: walletConnected && token.totalSupply !== \"Connect wallet to view\" ? (0,_config__WEBPACK_IMPORTED_MODULE_3__.isKnownToken)(network, token.address) ? 'Known + Live' : 'Factory' : 'Known Token'\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 782,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 781,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/tokens/\".concat(token.address),\n                                                        className: \"text-blue-600 hover:text-blue-900 mr-4\",\n                                                        children: \"View Details\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 798,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: getBlockExplorerUrl(network) + '/address/' + token.address,\n                                                        target: \"_blank\",\n                                                        rel: \"noopener noreferrer\",\n                                                        className: \"text-gray-600 hover:text-gray-900\",\n                                                        children: \"Explorer\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 804,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 797,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 739,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 737,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 714,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 text-center\",\n                        children: walletConnected ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mb-4\",\n                                    children: [\n                                        \"No tokens found on \",\n                                        network,\n                                        \" network.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 821,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mb-4\",\n                                    children: 'To add a token you\\'ve created, use the \"Add Token to Dashboard\" section above.'\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 822,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/modular-tokens\",\n                                    className: \"text-blue-600 hover:text-blue-900 font-medium\",\n                                    children: \"Create your first token\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 825,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: connectWallet,\n                            className: \"bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded\",\n                            children: \"Connect Wallet to View Tokens\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 833,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 818,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 667,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white shadow-md rounded-lg p-6 text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    href: \"/create-token?network=\".concat(network),\n                    className: \"bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded inline-block\",\n                    children: [\n                        \"Create New Token on \",\n                        network === 'amoy' ? 'Amoy Testnet' : 'Polygon Mainnet'\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 845,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 844,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 515,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"V66kPS2CT8ffZaZfOBQB+zLnAUs=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});