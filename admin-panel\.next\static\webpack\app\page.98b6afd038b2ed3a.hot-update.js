"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/constants/addresses.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/providers/provider-browser.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/address/checks.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/contract/contract.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/utils/units.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../config */ \"(app-pages-browser)/./src/config.ts\");\n/* harmony import */ var _contracts_SecurityTokenFactory_json__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../contracts/SecurityTokenFactory.json */ \"(app-pages-browser)/./src/contracts/SecurityTokenFactory.json\");\n/* harmony import */ var _contracts_SecurityToken_json__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../contracts/SecurityToken.json */ \"(app-pages-browser)/./src/contracts/SecurityToken.json\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction Home() {\n    _s();\n    const [tokens, setTokens] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [network, setNetwork] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('amoy');\n    const [factoryAddress, setFactoryAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [tokenImplementation, setTokenImplementation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [whitelistImplementation, setWhitelistImplementation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [verificationAddress, setVerificationAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [walletConnected, setWalletConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loadMethod, setLoadMethod] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('logs');\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            // Get contract addresses for current network\n            const addresses = (0,_config__WEBPACK_IMPORTED_MODULE_3__.getContractAddresses)(network);\n            setFactoryAddress(addresses.factory || '');\n            setTokenImplementation(addresses.tokenImplementation || '');\n            setWhitelistImplementation(addresses.whitelistImplementation || '');\n            checkWalletConnection();\n        }\n    }[\"Home.useEffect\"], [\n        network\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            if (walletConnected) {\n                fetchTokens();\n            } else {\n                // Load known tokens even without wallet connection for display\n                loadKnownTokensOnly();\n            }\n        }\n    }[\"Home.useEffect\"], [\n        walletConnected,\n        network\n    ]);\n    // Function to load only known tokens (for display without wallet)\n    const loadKnownTokensOnly = async ()=>{\n        setIsLoading(true);\n        setError(null);\n        try {\n            const knownTokens = (0,_config__WEBPACK_IMPORTED_MODULE_3__.getKnownTokens)(network);\n            if (knownTokens.length > 0) {\n                console.log(\"Loading \".concat(knownTokens.length, \" known tokens for display...\"));\n                // Create basic token objects from known tokens\n                const basicTokens = knownTokens.map((knownToken)=>({\n                        address: knownToken.address,\n                        name: knownToken.name,\n                        symbol: knownToken.symbol,\n                        maxSupply: \"Connect wallet to view\",\n                        totalSupply: \"Connect wallet to view\",\n                        whitelistAddress: ethers__WEBPACK_IMPORTED_MODULE_6__.ZeroAddress,\n                        tokenImageUrl: \"\"\n                    }));\n                setTokens(basicTokens);\n                setError(\"Connect your wallet to view live token data and access management features.\");\n            } else {\n                setTokens([]);\n                setError(\"No known tokens configured for this network. Connect your wallet to discover tokens from the factory.\");\n            }\n        } catch (error) {\n            console.error(\"Error loading known tokens:\", error);\n            setError(\"Error loading token information. Please try again.\");\n        }\n        setIsLoading(false);\n    };\n    const checkWalletConnection = async ()=>{\n        try {\n            if (window.ethereum) {\n                const provider = new ethers__WEBPACK_IMPORTED_MODULE_7__.BrowserProvider(window.ethereum);\n                const accounts = await provider.listAccounts();\n                if (accounts.length > 0) {\n                    setWalletConnected(true);\n                } else {\n                    setWalletConnected(false);\n                }\n            }\n        } catch (error) {\n            console.error(\"Error checking wallet connection:\", error);\n            setWalletConnected(false);\n        }\n    };\n    const connectWallet = async ()=>{\n        try {\n            if (window.ethereum) {\n                const provider = new ethers__WEBPACK_IMPORTED_MODULE_7__.BrowserProvider(window.ethereum);\n                await provider.send(\"eth_requestAccounts\", []);\n                setWalletConnected(true);\n            } else {\n                setError(\"Please install MetaMask to use this feature!\");\n            }\n        } catch (error) {\n            console.error(\"Error connecting wallet:\", error);\n        }\n    };\n    // Add your token address to verify\n    const addTokenManually = ()=>{\n        if (!verificationAddress) {\n            alert('Please enter a token address');\n            return;\n        }\n        if (!ethers__WEBPACK_IMPORTED_MODULE_8__.isAddress(verificationAddress)) {\n            alert('Please enter a valid address');\n            return;\n        }\n        loadTokenByAddress(verificationAddress);\n    };\n    const loadTokenByAddress = async (address)=>{\n        try {\n            setIsLoading(true);\n            if (!window.ethereum) {\n                throw new Error('Please install MetaMask to use this feature!');\n            }\n            // Use the new loadTokenDetails function\n            const tokenDetails = await loadTokenDetails(address);\n            if (tokenDetails) {\n                // Check if the token is already in the list\n                const exists = tokens.some((token)=>token.address.toLowerCase() === address.toLowerCase());\n                if (!exists) {\n                    setTokens((prevTokens)=>[\n                            ...prevTokens,\n                            tokenDetails\n                        ]);\n                }\n                setVerificationAddress('');\n            } else {\n                alert(\"Could not load token details. Is this a valid Security Token address?\");\n            }\n            setIsLoading(false);\n        } catch (err) {\n            console.error('Error loading token:', err);\n            setError(err.message || 'Error loading token. Please try again.');\n            setIsLoading(false);\n        }\n    };\n    const loadTokenDetails = async (address)=>{\n        try {\n            if (!window.ethereum) {\n                throw new Error('MetaMask not available');\n            }\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_7__.BrowserProvider(window.ethereum);\n            // Get the token contract\n            const tokenContract = new ethers__WEBPACK_IMPORTED_MODULE_9__.Contract(address, _contracts_SecurityToken_json__WEBPACK_IMPORTED_MODULE_5__.abi, provider);\n            // Read token details\n            const name = await tokenContract.name();\n            const symbol = await tokenContract.symbol();\n            // Get decimals to format amounts correctly\n            const decimalsRaw = await tokenContract.decimals();\n            const decimals = Number(decimalsRaw);\n            let maxSupply = \"0\";\n            let totalSupply = \"0\";\n            let whitelistAddress = \"******************************************\";\n            try {\n                const maxSupplyRaw = await tokenContract.maxSupply();\n                maxSupply = decimals === 0 ? maxSupplyRaw.toString() : ethers__WEBPACK_IMPORTED_MODULE_10__.formatUnits(maxSupplyRaw, decimals);\n            } catch (err) {\n                console.warn(\"Could not read maxSupply:\", err);\n            }\n            try {\n                const totalSupplyRaw = await tokenContract.totalSupply();\n                totalSupply = decimals === 0 ? totalSupplyRaw.toString() : ethers__WEBPACK_IMPORTED_MODULE_10__.formatUnits(totalSupplyRaw, decimals);\n            } catch (err) {\n                console.warn(\"Could not read totalSupply:\", err);\n            }\n            try {\n                whitelistAddress = await tokenContract.identityRegistry();\n            } catch (err) {\n                try {\n                    whitelistAddress = await tokenContract.whitelistAddress();\n                } catch (err2) {\n                    console.warn(\"Could not read whitelist address:\", err2);\n                }\n            }\n            // Try to get token image URL if supported\n            let tokenImageUrl = \"\";\n            try {\n                tokenImageUrl = await tokenContract.tokenImageUrl();\n            } catch (err) {\n                console.log(\"Token doesn't support image URL or image URL is empty\");\n            }\n            return {\n                address,\n                name,\n                symbol,\n                maxSupply,\n                totalSupply,\n                whitelistAddress,\n                tokenImageUrl\n            };\n        } catch (error) {\n            console.error(\"Error loading token details for \".concat(address, \":\"), error);\n            return null;\n        }\n    };\n    const fetchTokens = async ()=>{\n        setIsLoading(true);\n        setError(null);\n        setTokens([]);\n        try {\n            if (!window.ethereum) {\n                throw new Error('Please install MetaMask to use this feature!');\n            }\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_7__.BrowserProvider(window.ethereum);\n            const networkConfig = (0,_config__WEBPACK_IMPORTED_MODULE_3__.getNetworkConfig)(network);\n            const contractAddresses = (0,_config__WEBPACK_IMPORTED_MODULE_3__.getContractAddresses)(network);\n            if (!contractAddresses.factory) {\n                throw new Error(\"No factory address configured for network: \".concat(network));\n            }\n            // Connect to the factory contract\n            const factory = new ethers__WEBPACK_IMPORTED_MODULE_9__.Contract(contractAddresses.factory, _contracts_SecurityTokenFactory_json__WEBPACK_IMPORTED_MODULE_4__.abi, provider);\n            console.log(\"Loading tokens from factory:\", contractAddresses.factory);\n            console.log(\"Network:\", network);\n            console.log(\"Provider:\", provider);\n            try {\n                // Try to get tokens using the new enumeration methods\n                console.log(\"Attempting to fetch tokens using factory enumeration...\");\n                try {\n                    // First, try to get the token count (new factory enumeration)\n                    const tokenCount = await factory.getTokenCount();\n                    console.log(\"Factory reports \".concat(tokenCount, \" deployed tokens\"));\n                    if (tokenCount > 0) {\n                        // Get all token addresses at once for better performance\n                        const tokenAddresses = await factory.getAllDeployedTokens();\n                        console.log(\"Retrieved token addresses:\", tokenAddresses);\n                        // Load details for each token\n                        const tokenPromises = tokenAddresses.map(async (address)=>{\n                            try {\n                                return await loadTokenDetails(address);\n                            } catch (error) {\n                                console.warn(\"Failed to load token details for \".concat(address, \":\"), error);\n                                return null;\n                            }\n                        });\n                        const tokenResults = await Promise.all(tokenPromises);\n                        const validTokens = tokenResults.filter((token)=>token !== null);\n                        if (validTokens.length > 0) {\n                            setTokens(validTokens);\n                            console.log(\"Successfully loaded \".concat(validTokens.length, \" tokens from factory\"));\n                        } else {\n                            setError(\"Factory has tokens but could not load their details. Please check network connection.\");\n                        }\n                    } else {\n                        setError(\"No tokens found in factory. Create your first token to see it here.\");\n                    }\n                } catch (enumerationError) {\n                    console.warn(\"Factory enumeration failed, trying event-based discovery:\", enumerationError);\n                    // Fallback: Use event-based token discovery for older factories\n                    try {\n                        console.log(\"Searching for TokenDeployed events...\");\n                        // Get TokenDeployed events from the factory\n                        const filter = factory.filters.TokenDeployed();\n                        let events = [];\n                        // Try different block ranges to find events\n                        const ranges = [\n                            -10000,\n                            -50000\n                        ]; // Last 10k, then 50k blocks\n                        for (const range of ranges){\n                            try {\n                                events = await factory.queryFilter(filter, range);\n                                if (events.length > 0) break; // Found events, stop searching\n                            } catch (error) {\n                                console.warn(\"Failed to query \".concat(Math.abs(range), \" blocks:\"), error);\n                            }\n                        }\n                        console.log(\"Found \".concat(events.length, \" TokenDeployed events\"));\n                        if (events.length > 0) {\n                            // Extract unique token addresses from events\n                            const tokenAddresses = [\n                                ...new Set(events.map((event)=>{\n                                    var _event_args;\n                                    return (_event_args = event.args) === null || _event_args === void 0 ? void 0 : _event_args.tokenAddress;\n                                }).filter(Boolean))\n                            ];\n                            console.log(\"Token addresses from events:\", tokenAddresses);\n                            // Load details for each token\n                            const tokenPromises = tokenAddresses.map(async (address)=>{\n                                try {\n                                    return await loadTokenDetails(address);\n                                } catch (error) {\n                                    console.warn(\"Failed to load token details for \".concat(address, \":\"), error);\n                                    return null;\n                                }\n                            });\n                            const tokenResults = await Promise.all(tokenPromises);\n                            const validTokens = tokenResults.filter((token)=>token !== null);\n                            if (validTokens.length > 0) {\n                                setTokens(validTokens);\n                                console.log(\"Successfully loaded \".concat(validTokens.length, \" tokens from events\"));\n                            } else {\n                                setError(\"Found token events but could not load token details. Please check network connection.\");\n                            }\n                        } else {\n                            setError(\"No tokens found in factory events. Create your first token to see it here.\");\n                        }\n                    } catch (eventError) {\n                        console.warn(\"Event-based discovery failed:\", eventError);\n                        // Final fallback: Load known tokens from configuration\n                        console.log(\"Falling back to known tokens from configuration...\");\n                        const knownTokens = (0,_config__WEBPACK_IMPORTED_MODULE_3__.getKnownTokens)(network);\n                        if (knownTokens.length > 0) {\n                            console.log(\"Loading \".concat(knownTokens.length, \" known tokens...\"));\n                            const knownTokenPromises = knownTokens.map(async (knownToken)=>{\n                                try {\n                                    const tokenDetails = await loadTokenDetails(knownToken.address);\n                                    return tokenDetails;\n                                } catch (error) {\n                                    console.warn(\"Failed to load known token \".concat(knownToken.address, \":\"), error);\n                                    // Return basic info even if contract call fails\n                                    return {\n                                        address: knownToken.address,\n                                        name: knownToken.name,\n                                        symbol: knownToken.symbol,\n                                        maxSupply: \"Unknown\",\n                                        totalSupply: \"Unknown\",\n                                        whitelistAddress: ethers__WEBPACK_IMPORTED_MODULE_6__.ZeroAddress,\n                                        tokenImageUrl: \"\"\n                                    };\n                                }\n                            });\n                            const knownTokenResults = await Promise.all(knownTokenPromises);\n                            const validKnownTokens = knownTokenResults.filter((token)=>token !== null);\n                            if (validKnownTokens.length > 0) {\n                                setTokens(validKnownTokens);\n                                console.log(\"Successfully loaded \".concat(validKnownTokens.length, \" known tokens\"));\n                                setError(\"Factory enumeration failed, but loaded known tokens. You can add more tokens manually below.\");\n                            } else {\n                                setError(\"Could not load tokens from factory or known tokens. Please add token addresses manually using the tool below.\");\n                            }\n                        } else {\n                            setError(\"Could not load tokens from factory. Please add token addresses manually using the tool below.\");\n                        }\n                    }\n                }\n            } catch (err) {\n                console.error(\"Error loading tokens:\", err);\n                const errorMessage = err instanceof Error ? err.message : \"Unknown error\";\n                // Try to load known tokens as fallback even on factory connection error\n                console.log(\"Factory connection failed, trying known tokens...\");\n                const knownTokens = (0,_config__WEBPACK_IMPORTED_MODULE_3__.getKnownTokens)(network);\n                if (knownTokens.length > 0) {\n                    try {\n                        const knownTokenPromises = knownTokens.map(async (knownToken)=>{\n                            try {\n                                const tokenDetails = await loadTokenDetails(knownToken.address);\n                                return tokenDetails;\n                            } catch (error) {\n                                console.warn(\"Failed to load known token \".concat(knownToken.address, \":\"), error);\n                                return {\n                                    address: knownToken.address,\n                                    name: knownToken.name,\n                                    symbol: knownToken.symbol,\n                                    maxSupply: \"Unknown\",\n                                    totalSupply: \"Unknown\",\n                                    whitelistAddress: ethers__WEBPACK_IMPORTED_MODULE_6__.ZeroAddress,\n                                    tokenImageUrl: \"\"\n                                };\n                            }\n                        });\n                        const knownTokenResults = await Promise.all(knownTokenPromises);\n                        const validKnownTokens = knownTokenResults.filter((token)=>token !== null);\n                        if (validKnownTokens.length > 0) {\n                            setTokens(validKnownTokens);\n                            console.log(\"Loaded \".concat(validKnownTokens.length, \" known tokens as fallback\"));\n                            setError(\"Factory connection failed, but loaded known tokens. Please check your network connection.\");\n                        } else {\n                            setError(\"Could not load tokens from factory or known tokens. Please add token addresses manually using the verify tool below.\");\n                        }\n                    } catch (fallbackError) {\n                        console.error(\"Fallback token loading failed:\", fallbackError);\n                        setError(\"Could not load tokens from factory or known tokens. Please add token addresses manually using the verify tool below.\");\n                    }\n                } else {\n                    setError(\"Could not load tokens from factory. Please add token addresses manually using the verify tool below.\");\n                }\n            }\n            setIsLoading(false);\n        } catch (err) {\n            console.error('Error fetching tokens:', err);\n            setError(err.message || 'Error fetching tokens. Please try again.');\n            setIsLoading(false);\n        }\n    };\n    const handleNetworkChange = (event)=>{\n        const newNetwork = event.target.value;\n        console.log(\"Switching to network: \".concat(newNetwork));\n        setNetwork(newNetwork);\n    };\n    const verifyToken = ()=>{\n        if (!verificationAddress) {\n            alert('Please enter a token address to verify');\n            return;\n        }\n        let explorerUrl = '';\n        if (network === 'amoy') {\n            explorerUrl = \"https://www.oklink.com/amoy/address/\".concat(verificationAddress);\n        } else if (network === 'polygon') {\n            explorerUrl = \"https://polygonscan.com/address/\".concat(verificationAddress);\n        }\n        if (explorerUrl) {\n            window.open(explorerUrl, '_blank');\n        }\n    };\n    const getBlockExplorerUrl = (network)=>{\n        if (network === 'amoy') {\n            return 'https://www.oklink.com/amoy';\n        } else if (network === 'polygon') {\n            return 'https://polygonscan.com';\n        }\n        return '#';\n    };\n    const viewFactoryTokens = ()=>{\n        let explorerUrl = '';\n        if (network === 'amoy') {\n            explorerUrl = \"https://www.oklink.com/amoy/address/\".concat(factoryAddress, \"#eventlog\");\n        } else if (network === 'polygon') {\n            explorerUrl = \"https://polygonscan.com/address/\".concat(factoryAddress, \"#events\");\n        }\n        if (explorerUrl) {\n            window.open(explorerUrl, '_blank');\n        }\n    };\n    const formatNumber = (value)=>{\n        // Format the number with commas\n        const number = parseFloat(value);\n        return number.toLocaleString(undefined, {\n            maximumFractionDigits: 0\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold mb-2\",\n                        children: \"Security Token Dashboard\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 517,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"View and manage your security tokens\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 518,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 516,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 flex justify-between items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"network\",\n                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                children: \"Network\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 525,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                id: \"network\",\n                                className: \"p-2 border border-gray-300 rounded-md\",\n                                value: network,\n                                onChange: handleNetworkChange,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"amoy\",\n                                        children: \"Amoy Testnet\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 534,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"polygon\",\n                                        children: \"Polygon Mainnet\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 535,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 528,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 524,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-3\",\n                        children: [\n                            !walletConnected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: connectWallet,\n                                className: \"bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded transition\",\n                                children: \"Connect Wallet\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 541,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/modular-tokens\",\n                                className: \"bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded transition\",\n                                children: \"Create New Token\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 548,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 539,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 523,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: error\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 559,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 558,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-green-50 border-l-4 border-green-500 text-green-700 p-4 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"font-bold mb-2\",\n                        children: \"Connected to Your Deployed Factory Contract\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 564,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4 mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm font-semibold\",\n                                        children: \"Factory Address:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 568,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"font-mono text-sm\",\n                                        children: factoryAddress\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 569,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 567,\n                                columnNumber: 11\n                            }, this),\n                            tokenImplementation && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm font-semibold\",\n                                        children: \"Token Implementation:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 574,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"font-mono text-sm\",\n                                        children: tokenImplementation\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 575,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 573,\n                                columnNumber: 13\n                            }, this),\n                            whitelistImplementation && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm font-semibold\",\n                                        children: \"Whitelist Implementation:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 581,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"font-mono text-sm\",\n                                        children: whitelistImplementation\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 582,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 580,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 566,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4 mt-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"\".concat(getBlockExplorerUrl(network), \"/address/\").concat(factoryAddress),\n                                target: \"_blank\",\n                                rel: \"noopener noreferrer\",\n                                className: \"text-green-700 underline hover:text-green-900\",\n                                children: [\n                                    \"View Factory on \",\n                                    network === 'amoy' ? 'OKLink Explorer' : 'PolygonScan'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 588,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: viewFactoryTokens,\n                                className: \"bg-green-600 hover:bg-green-700 text-white py-1 px-3 rounded text-sm\",\n                                children: \"View All Factory Token Deployments\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 597,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 587,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 563,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white shadow-md rounded-lg p-6 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-xl font-semibold mb-4\",\n                        children: \"Quick Actions\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 607,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap gap-3 mb-6 p-4 bg-gray-50 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/create-token\",\n                                className: \"bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded text-sm font-medium\",\n                                children: \"\\uD83D\\uDE80 Create Token\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 611,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/transfer-controls\",\n                                className: \"bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded text-sm font-medium\",\n                                children: \"\\uD83D\\uDD12 Transfer Controls\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 617,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/clients\",\n                                className: \"bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded text-sm font-medium\",\n                                children: \"\\uD83D\\uDC65 Client Management\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 623,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>window.open('https://amoy.polygonscan.com/', '_blank'),\n                                className: \"bg-gray-600 hover:bg-gray-700 text-white py-2 px-4 rounded text-sm font-medium\",\n                                children: \"\\uD83D\\uDD0D Block Explorer\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 629,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 610,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-lg font-medium mb-2\",\n                        children: \"Add Token to Dashboard\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 637,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mb-4 text-gray-600\",\n                        children: \"Manually add a token by entering its address below.\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 638,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                value: verificationAddress,\n                                onChange: (e)=>setVerificationAddress(e.target.value),\n                                placeholder: \"Enter token address (0x...)\",\n                                className: \"flex-grow p-2 border border-gray-300 rounded\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 642,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: addTokenManually,\n                                className: \"bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded\",\n                                children: \"Add Token\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 649,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: verifyToken,\n                                className: \"bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded\",\n                                children: \"Verify on Explorer\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 655,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 641,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-2 text-xs text-gray-500\",\n                        children: \"Note: After creating a token, you can add it here to display it on the dashboard.\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 662,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 606,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white shadow-md rounded-lg overflow-hidden mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 border-b\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-start\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-semibold\",\n                                                children: \"Security Token Dashboard\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 671,\n                                                columnNumber: 15\n                                            }, this),\n                                            walletConnected ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600 mt-1\",\n                                                children: [\n                                                    \"Live data from \",\n                                                    network === 'amoy' ? 'Amoy Testnet' : 'Polygon Mainnet',\n                                                    \" factory\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 673,\n                                                columnNumber: 17\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600 mt-1\",\n                                                children: \"Showing known tokens - connect wallet for live data\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 677,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 670,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-3 h-3 rounded-full \".concat(walletConnected ? 'bg-green-500' : 'bg-yellow-500')\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 683,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: walletConnected ? 'Connected' : 'Offline Mode'\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 684,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 682,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 669,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-2 mt-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: walletConnected ? fetchTokens : loadKnownTokensOnly,\n                                        className: \"text-sm bg-blue-500 hover:bg-blue-600 text-white py-1 px-2 rounded\",\n                                        disabled: isLoading,\n                                        children: isLoading ? 'Loading...' : 'Refresh Token List'\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 691,\n                                        columnNumber: 13\n                                    }, this),\n                                    !walletConnected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: connectWallet,\n                                        className: \"text-sm bg-green-500 hover:bg-green-600 text-white py-1 px-2 rounded\",\n                                        children: \"Connect for Live Data\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 699,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 690,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 668,\n                        columnNumber: 9\n                    }, this),\n                    isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center my-12\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 711,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 710,\n                        columnNumber: 11\n                    }, this) : tokens.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                        className: \"min-w-full divide-y divide-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                className: \"bg-gray-50\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"Name\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 717,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"Symbol\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 720,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"Total Supply\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 723,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"Max Supply\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 726,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"Source\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 729,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"Actions\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 732,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 716,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 715,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                className: \"bg-white divide-y divide-gray-200\",\n                                children: tokens.map((token, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        className: \"hover:bg-gray-50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-shrink-0 h-10 w-10\",\n                                                            children: [\n                                                                token.tokenImageUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                    className: \"h-10 w-10 rounded-full object-cover border-2 border-gray-200\",\n                                                                    src: token.tokenImageUrl,\n                                                                    alt: \"\".concat(token.name, \" logo\"),\n                                                                    onError: (e)=>{\n                                                                        var _target_nextElementSibling;\n                                                                        // Fallback to default icon if image fails to load\n                                                                        const target = e.target;\n                                                                        target.style.display = 'none';\n                                                                        (_target_nextElementSibling = target.nextElementSibling) === null || _target_nextElementSibling === void 0 ? void 0 : _target_nextElementSibling.classList.remove('hidden');\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 745,\n                                                                    columnNumber: 27\n                                                                }, this) : null,\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center \".concat(token.tokenImageUrl ? 'hidden' : ''),\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium text-gray-700\",\n                                                                        children: token.symbol.substring(0, 2).toUpperCase()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 759,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 758,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 743,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"ml-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm font-medium text-gray-900\",\n                                                                    children: token.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 765,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-gray-500 truncate\",\n                                                                    title: token.address,\n                                                                    children: [\n                                                                        token.address.substring(0, 10),\n                                                                        \"...\",\n                                                                        token.address.substring(token.address.length - 8)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 766,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 764,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 741,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 740,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                children: token.symbol\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 772,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                children: token.totalSupply ? formatNumber(token.totalSupply) : \"—\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 775,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                children: token.maxSupply ? formatNumber(token.maxSupply) : \"—\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 778,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \".concat(walletConnected && token.totalSupply !== \"Connect wallet to view\" ? (0,_config__WEBPACK_IMPORTED_MODULE_3__.isKnownToken)(network, token.address) ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'),\n                                                    children: walletConnected && token.totalSupply !== \"Connect wallet to view\" ? (0,_config__WEBPACK_IMPORTED_MODULE_3__.isKnownToken)(network, token.address) ? 'Known + Live' : 'Factory' : 'Known Token'\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 782,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 781,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/tokens/\".concat(token.address),\n                                                        className: \"text-blue-600 hover:text-blue-900 mr-4\",\n                                                        children: \"View Details\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 798,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: getBlockExplorerUrl(network) + '/address/' + token.address,\n                                                        target: \"_blank\",\n                                                        rel: \"noopener noreferrer\",\n                                                        className: \"text-gray-600 hover:text-gray-900\",\n                                                        children: \"Explorer\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 804,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 797,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 739,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 737,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 714,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 text-center\",\n                        children: walletConnected ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mb-4\",\n                                    children: [\n                                        \"No tokens found on \",\n                                        network,\n                                        \" network.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 821,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mb-4\",\n                                    children: 'To add a token you\\'ve created, use the \"Add Token to Dashboard\" section above.'\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 822,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/create-token?network=\".concat(network),\n                                    className: \"text-blue-600 hover:text-blue-900 font-medium\",\n                                    children: \"Create your first token\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 825,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: connectWallet,\n                            className: \"bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded\",\n                            children: \"Connect Wallet to View Tokens\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 833,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 818,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 667,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white shadow-md rounded-lg p-6 text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    href: \"/create-token?network=\".concat(network),\n                    className: \"bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded inline-block\",\n                    children: [\n                        \"Create New Token on \",\n                        network === 'amoy' ? 'Amoy Testnet' : 'Polygon Mainnet'\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 845,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 844,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 515,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"V66kPS2CT8ffZaZfOBQB+zLnAUs=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});