"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/create-modular-token/page",{

/***/ "(app-pages-browser)/./src/app/create-modular-token/page.tsx":
/*!***********************************************!*\
  !*** ./src/app/create-modular-token/page.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CreateModularTokenPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/providers/provider-browser.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/utils/units.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/contract/contract.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n// Contract addresses from environment\nconst SECURITY_TOKEN_CORE_ADDRESS = \"******************************************\";\nconst UPGRADE_MANAGER_ADDRESS = \"******************************************\";\nconst MODULAR_TOKEN_FACTORY_ADDRESS = \"******************************************\";\nfunction CreateModularTokenPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    // State Management\n    const [provider, setProvider] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [signer, setSigner] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [deployedToken, setDeployedToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [hasDeployerRole, setHasDeployerRole] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isCheckingRole, setIsCheckingRole] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        symbol: '',\n        decimals: 0,\n        maxSupply: '10000000',\n        adminAddress: '',\n        tokenPrice: '1.00',\n        currency: 'USD',\n        bonusTiers: 'Early Bird: 10%, Standard: 5%, Late: 0%',\n        tokenDetails: 'Security token with advanced compliance features',\n        tokenImageUrl: ''\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CreateModularTokenPage.useEffect\": ()=>{\n            initializeProvider();\n        }\n    }[\"CreateModularTokenPage.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CreateModularTokenPage.useEffect\": ()=>{\n            if (signer && MODULAR_TOKEN_FACTORY_ADDRESS) {\n                checkDeployerRole();\n            }\n        }\n    }[\"CreateModularTokenPage.useEffect\"], [\n        signer,\n        MODULAR_TOKEN_FACTORY_ADDRESS\n    ]);\n    const initializeProvider = async ()=>{\n        try {\n            if ( true && window.ethereum) {\n                const provider = new ethers__WEBPACK_IMPORTED_MODULE_4__.BrowserProvider(window.ethereum);\n                await provider.send('eth_requestAccounts', []);\n                // Check network\n                const network = await provider.getNetwork();\n                console.log('Connected to network:', network.name, 'Chain ID:', network.chainId.toString());\n                // Amoy testnet chain ID is 80002\n                if (network.chainId !== 80002n) {\n                    setError(\"Wrong network! Please switch to Amoy testnet (Chain ID: 80002). Currently on: \".concat(network.chainId.toString()));\n                    return;\n                }\n                const signer = await provider.getSigner();\n                const address = await signer.getAddress();\n                // Check balance\n                const balance = await provider.getBalance(address);\n                console.log('Wallet balance:', ethers__WEBPACK_IMPORTED_MODULE_5__.formatEther(balance), 'ETH');\n                if (balance < ethers__WEBPACK_IMPORTED_MODULE_5__.parseEther('0.01')) {\n                    setError('Insufficient balance. You need at least 0.01 ETH for gas fees.');\n                    return;\n                }\n                setProvider(provider);\n                setSigner(signer);\n                setFormData((prev)=>({\n                        ...prev,\n                        adminAddress: address\n                    }));\n            } else {\n                setError('MetaMask not found. Please install MetaMask to create tokens.');\n            }\n        } catch (error) {\n            console.error('Error initializing provider:', error);\n            setError('Failed to connect to wallet');\n        }\n    };\n    const checkDeployerRole = async ()=>{\n        if (!signer || !MODULAR_TOKEN_FACTORY_ADDRESS) return;\n        try {\n            const ModularTokenFactoryABI = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_contracts_ModularTokenFactory_json\").then(__webpack_require__.t.bind(__webpack_require__, /*! ../../contracts/ModularTokenFactory.json */ \"(app-pages-browser)/./src/contracts/ModularTokenFactory.json\", 19));\n            const factory = new ethers__WEBPACK_IMPORTED_MODULE_6__.Contract(MODULAR_TOKEN_FACTORY_ADDRESS, ModularTokenFactoryABI.default, signer);\n            const DEPLOYER_ROLE = await factory.DEPLOYER_ROLE();\n            const userAddress = await signer.getAddress();\n            const hasRole = await factory.hasRole(DEPLOYER_ROLE, userAddress);\n            setHasDeployerRole(hasRole);\n        } catch (error) {\n            console.error('Error checking deployer role:', error);\n            setHasDeployerRole(false);\n        }\n    };\n    const requestDeployerRole = async ()=>{\n        if (!signer || !MODULAR_TOKEN_FACTORY_ADDRESS) return;\n        setIsCheckingRole(true);\n        setError(null);\n        try {\n            const ModularTokenFactoryABI = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_contracts_ModularTokenFactory_json\").then(__webpack_require__.t.bind(__webpack_require__, /*! ../../contracts/ModularTokenFactory.json */ \"(app-pages-browser)/./src/contracts/ModularTokenFactory.json\", 19));\n            const factory = new ethers__WEBPACK_IMPORTED_MODULE_6__.Contract(MODULAR_TOKEN_FACTORY_ADDRESS, ModularTokenFactoryABI.default, signer);\n            const DEPLOYER_ROLE = await factory.DEPLOYER_ROLE();\n            const userAddress = await signer.getAddress();\n            // Try to grant the role (this will only work if the user has admin role)\n            const tx = await factory.grantRole(DEPLOYER_ROLE, userAddress);\n            await tx.wait();\n            setHasDeployerRole(true);\n            setSuccess('Deployer role granted successfully!');\n        } catch (error) {\n            console.error('Error granting deployer role:', error);\n            if (error.message.includes('AccessControlUnauthorizedAccount')) {\n                setError('You do not have permission to grant roles. Please contact an administrator to grant you the DEPLOYER_ROLE.');\n            } else {\n                setError(\"Failed to grant deployer role: \".concat(error.message));\n            }\n        } finally{\n            setIsCheckingRole(false);\n        }\n    };\n    const handleInputChange = (e)=>{\n        const { name, value, type } = e.target;\n        let processedValue = value;\n        if (name === 'decimals') {\n            processedValue = parseInt(value, 10);\n        }\n        setFormData({\n            ...formData,\n            [name]: processedValue\n        });\n    };\n    const deployModularToken = async ()=>{\n        if (!signer || !MODULAR_TOKEN_FACTORY_ADDRESS) {\n            setError('Please connect your wallet first');\n            return;\n        }\n        setIsSubmitting(true);\n        setError(null);\n        setSuccess(null);\n        try {\n            // Import the ModularTokenFactory ABI\n            const ModularTokenFactoryABI = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_contracts_ModularTokenFactory_json\").then(__webpack_require__.t.bind(__webpack_require__, /*! ../../contracts/ModularTokenFactory.json */ \"(app-pages-browser)/./src/contracts/ModularTokenFactory.json\", 19));\n            // Create factory contract instance\n            const factory = new ethers__WEBPACK_IMPORTED_MODULE_6__.Contract(MODULAR_TOKEN_FACTORY_ADDRESS, ModularTokenFactoryABI.default, signer);\n            // Check if user has DEPLOYER_ROLE\n            if (hasDeployerRole === false) {\n                setError('You do not have permission to deploy tokens. Please contact an administrator to grant you the DEPLOYER_ROLE.');\n                return;\n            }\n            // Double-check role if we haven't checked yet\n            if (hasDeployerRole === null) {\n                const DEPLOYER_ROLE = await factory.DEPLOYER_ROLE();\n                const userAddress = await signer.getAddress();\n                const roleCheck = await factory.hasRole(DEPLOYER_ROLE, userAddress);\n                if (!roleCheck) {\n                    setHasDeployerRole(false);\n                    setError('You do not have permission to deploy tokens. Please contact an administrator to grant you the DEPLOYER_ROLE.');\n                    return;\n                }\n                setHasDeployerRole(true);\n            }\n            // Convert max supply to proper units\n            const maxSupplyWei = ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits(formData.maxSupply, formData.decimals);\n            // Combine price and currency\n            const tokenPriceWithCurrency = \"\".concat(formData.tokenPrice, \" \").concat(formData.currency);\n            // Deploy the token through the factory\n            console.log('Deploying token with params:', {\n                name: formData.name,\n                symbol: formData.symbol,\n                decimals: formData.decimals,\n                maxSupply: formData.maxSupply,\n                admin: formData.adminAddress,\n                tokenPrice: tokenPriceWithCurrency,\n                bonusTiers: formData.bonusTiers,\n                tokenDetails: formData.tokenDetails,\n                tokenImageUrl: formData.tokenImageUrl\n            });\n            // First try to estimate gas\n            let gasEstimate;\n            let useFixedGas = false;\n            try {\n                gasEstimate = await factory.deployToken.estimateGas(formData.name, formData.symbol, formData.decimals, maxSupplyWei, formData.adminAddress, tokenPriceWithCurrency, formData.bonusTiers, formData.tokenDetails, formData.tokenImageUrl);\n                console.log('Gas estimate:', gasEstimate.toString());\n            } catch (gasError) {\n                console.warn('Gas estimation failed, using fixed gas limit:', gasError.message);\n                gasEstimate = BigInt(1000000); // 1M gas as conservative fallback\n                useFixedGas = true;\n            }\n            // Add 50% buffer to gas estimate for safety\n            const gasLimit = useFixedGas ? gasEstimate : gasEstimate + gasEstimate * BigInt(50) / BigInt(100);\n            console.log('Using gas limit:', gasLimit.toString());\n            // Try deployment with minimal transaction options to let MetaMask handle gas\n            let tx;\n            try {\n                // First attempt: Let MetaMask estimate gas\n                tx = await factory.deployToken(formData.name, formData.symbol, formData.decimals, maxSupplyWei, formData.adminAddress, tokenPriceWithCurrency, formData.bonusTiers, formData.tokenDetails, formData.tokenImageUrl);\n            } catch (firstError) {\n                console.warn('First attempt failed, trying with manual gas settings:', firstError.message);\n                // Second attempt: Use manual gas settings\n                const feeData = await signer.provider.getFeeData();\n                const txOptions = {\n                    gasLimit: gasLimit,\n                    maxFeePerGas: feeData.maxFeePerGas || ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits('60', 'gwei'),\n                    maxPriorityFeePerGas: feeData.maxPriorityFeePerGas || ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits('2', 'gwei')\n                };\n                console.log('Retry with transaction options:', {\n                    gasLimit: txOptions.gasLimit.toString(),\n                    maxFeePerGas: ethers__WEBPACK_IMPORTED_MODULE_5__.formatUnits(txOptions.maxFeePerGas, 'gwei') + ' gwei',\n                    maxPriorityFeePerGas: ethers__WEBPACK_IMPORTED_MODULE_5__.formatUnits(txOptions.maxPriorityFeePerGas, 'gwei') + ' gwei'\n                });\n                tx = await factory.deployToken(formData.name, formData.symbol, formData.decimals, maxSupplyWei, formData.adminAddress, tokenPriceWithCurrency, formData.bonusTiers, formData.tokenDetails, formData.tokenImageUrl, txOptions);\n            }\n            console.log('Transaction sent:', tx.hash);\n            setSuccess('Transaction sent! Waiting for confirmation...');\n            const receipt = await tx.wait();\n            console.log('Transaction confirmed:', receipt);\n            // Get the deployed token address from the event\n            const event = receipt.logs.find((log)=>{\n                try {\n                    const parsed = factory.interface.parseLog(log);\n                    return parsed.name === 'TokenDeployed';\n                } catch (e) {\n                    return false;\n                }\n            });\n            if (event) {\n                const parsedEvent = factory.interface.parseLog(event);\n                const tokenAddress = parsedEvent.args.tokenAddress;\n                setDeployedToken({\n                    address: tokenAddress,\n                    name: formData.name,\n                    symbol: formData.symbol,\n                    transactionHash: tx.hash\n                });\n                setSuccess(\"Modular token deployed successfully! Address: \".concat(tokenAddress));\n            } else {\n                setError('Token deployed but could not find deployment event. Please check the transaction.');\n            }\n        } catch (error) {\n            var _error_error_message, _error_error;\n            console.error('Error deploying modular token:', error);\n            // Parse common error messages\n            let errorMessage = error.message;\n            if (error.code === 'UNKNOWN_ERROR' && ((_error_error = error.error) === null || _error_error === void 0 ? void 0 : (_error_error_message = _error_error.message) === null || _error_error_message === void 0 ? void 0 : _error_error_message.includes('Internal JSON-RPC error'))) {\n                errorMessage = 'Network error: The Amoy testnet is experiencing issues. Please try again in a few minutes.';\n            } else if (error.message.includes('AccessControlUnauthorizedAccount')) {\n                errorMessage = 'You do not have permission to deploy tokens. Please contact an administrator.';\n            } else if (error.message.includes('execution reverted')) {\n                errorMessage = 'Transaction failed. Please check your inputs and try again.';\n            } else if (error.message.includes('gas')) {\n                errorMessage = 'Gas estimation failed. The network may be congested. Please try again.';\n            } else if (error.message.includes('fee') && error.message.includes('cap')) {\n                errorMessage = 'Transaction fee too high. Please try again when network fees are lower.';\n            } else if (error.code === 'NETWORK_ERROR') {\n                errorMessage = 'Network connection error. Please check your internet connection and try again.';\n            } else if (error.code === 'TIMEOUT') {\n                errorMessage = 'Transaction timeout. The network may be slow. Please try again.';\n            }\n            setError(\"Failed to deploy modular token: \".concat(errorMessage));\n            // Log additional debugging info\n            console.log('Error details:', {\n                code: error.code,\n                reason: error.reason,\n                data: error.data,\n                transaction: error.transaction\n            });\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        // Validation\n        if (!formData.name || !formData.symbol || !formData.adminAddress) {\n            setError('Please fill in all required fields');\n            return;\n        }\n        if (formData.decimals < 0 || formData.decimals > 18) {\n            setError('Decimals must be between 0 and 18');\n            return;\n        }\n        await deployModularToken();\n    };\n    if (!provider || !signer) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6 flex items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/\",\n                            className: \"text-blue-600 hover:text-blue-800 mr-4\",\n                            children: \"← Back to Dashboard\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                            lineNumber: 404,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold\",\n                            children: \"Create Modular Security Token\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                            lineNumber: 407,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                    lineNumber: 403,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-bold mb-2\",\n                            children: \"Connect Wallet\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                            lineNumber: 411,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-4\",\n                            children: \"Please connect your MetaMask wallet to create modular tokens.\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                            lineNumber: 412,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: initializeProvider,\n                            className: \"bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded\",\n                            children: \"Connect Wallet\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                            lineNumber: 415,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                    lineNumber: 410,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n            lineNumber: 402,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 flex items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/\",\n                        className: \"text-blue-600 hover:text-blue-800 mr-4\",\n                        children: \"← Back to Dashboard\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                        lineNumber: 429,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold\",\n                        children: \"Create Modular Security Token\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                        lineNumber: 432,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"ml-4 px-3 py-1 text-xs font-medium rounded-full bg-purple-100 text-purple-800\",\n                        children: \"Amoy Testnet\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                        lineNumber: 433,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                lineNumber: 428,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 p-4 bg-purple-50 border border-purple-200 rounded-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-shrink-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-purple-600 text-xl\",\n                                children: \"\\uD83D\\uDE80\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                lineNumber: 442,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                            lineNumber: 441,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"ml-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-sm font-medium text-purple-800\",\n                                    children: \"Next-Generation Modular Architecture\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                    lineNumber: 445,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-1 text-sm text-purple-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"Create tokens using our advanced modular architecture with:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                            lineNumber: 449,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"mt-1 list-disc list-inside space-y-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Upgradeable Contracts:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                            lineNumber: 451,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" Future-proof with secure upgrade mechanisms\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                    lineNumber: 451,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Modular Design:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                            lineNumber: 452,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" Add new features without redeployment\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                    lineNumber: 452,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Enhanced Security:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                            lineNumber: 453,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" Timelock protection and emergency controls\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                    lineNumber: 453,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Gas Optimization:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                            lineNumber: 454,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" Efficient proxy pattern implementation\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                    lineNumber: 454,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                            lineNumber: 450,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                    lineNumber: 448,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                            lineNumber: 444,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                    lineNumber: 440,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                lineNumber: 439,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-sm font-medium text-blue-800 mb-2\",\n                        children: \"Modular Architecture Contracts\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                        lineNumber: 463,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-blue-700 space-y-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"ModularTokenFactory:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 466,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-2 font-mono text-xs\",\n                                        children: MODULAR_TOKEN_FACTORY_ADDRESS\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 467,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                lineNumber: 465,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"SecurityTokenCore Implementation:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 470,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-2 font-mono text-xs\",\n                                        children: SECURITY_TOKEN_CORE_ADDRESS\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 471,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                lineNumber: 469,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"UpgradeManager:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 474,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-2 font-mono text-xs\",\n                                        children: UPGRADE_MANAGER_ADDRESS\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 475,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                lineNumber: 473,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                        lineNumber: 464,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                lineNumber: 462,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 p-4 bg-gray-50 border border-gray-200 rounded-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-sm font-medium text-gray-800 mb-2\",\n                        children: \"Deployment Permissions\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                        lineNumber: 482,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-700\",\n                                        children: \"DEPLOYER_ROLE Status:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 485,\n                                        columnNumber: 13\n                                    }, this),\n                                    hasDeployerRole === null ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-yellow-600\",\n                                        children: \"⏳ Checking...\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 487,\n                                        columnNumber: 15\n                                    }, this) : hasDeployerRole ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-green-600\",\n                                        children: \"✅ Granted\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 489,\n                                        columnNumber: 15\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-red-600\",\n                                        children: \"❌ Not Granted\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 491,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                lineNumber: 484,\n                                columnNumber: 11\n                            }, this),\n                            hasDeployerRole === false && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: requestDeployerRole,\n                                disabled: isCheckingRole,\n                                className: \"bg-blue-600 hover:bg-blue-700 text-white font-medium py-1 px-3 rounded text-sm disabled:opacity-50\",\n                                children: isCheckingRole ? '⏳ Requesting...' : '🔑 Request Role'\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                lineNumber: 495,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                        lineNumber: 483,\n                        columnNumber: 9\n                    }, this),\n                    hasDeployerRole === false && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs text-gray-600 mt-2\",\n                        children: \"You need the DEPLOYER_ROLE to create tokens. Contact an administrator if the request fails.\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                        lineNumber: 505,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                lineNumber: 481,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-red-600 mr-2\",\n                            children: \"⚠️\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                            lineNumber: 515,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                            lineNumber: 516,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                    lineNumber: 514,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                lineNumber: 513,\n                columnNumber: 9\n            }, this),\n            success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-green-600 mr-2\",\n                            children: \"✅\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                            lineNumber: 525,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: success\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                            lineNumber: 526,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                    lineNumber: 524,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                lineNumber: 523,\n                columnNumber: 9\n            }, this),\n            deployedToken && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 bg-green-50 border border-green-200 rounded-lg p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-green-800 mb-4\",\n                        children: \"\\uD83C\\uDF89 Modular Token Created Successfully!\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                        lineNumber: 534,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2 text-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"Token Name:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 537,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: deployedToken.name\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 538,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                lineNumber: 536,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"Symbol:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 541,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: deployedToken.symbol\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 542,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                lineNumber: 540,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"Contract Address:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 545,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-mono text-xs\",\n                                        children: deployedToken.address\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 546,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                lineNumber: 544,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                        lineNumber: 535,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 flex gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>window.open(\"https://amoy.polygonscan.com/address/\".concat(deployedToken.address), '_blank'),\n                                className: \"bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded text-sm\",\n                                children: \"View on PolygonScan\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                lineNumber: 550,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/modular-tokens\",\n                                className: \"bg-purple-600 hover:bg-purple-700 text-white font-medium py-2 px-4 rounded text-sm\",\n                                children: \"Manage Token\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                lineNumber: 556,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                        lineNumber: 549,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                lineNumber: 533,\n                columnNumber: 9\n            }, this),\n            !deployedToken && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-bold mb-4\",\n                        children: \"Token Configuration\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                        lineNumber: 569,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit,\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"name\",\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Token Name *\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                lineNumber: 574,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                id: \"name\",\n                                                name: \"name\",\n                                                value: formData.name,\n                                                onChange: handleInputChange,\n                                                placeholder: \"e.g., Augment Security Token\",\n                                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                lineNumber: 577,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 573,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"symbol\",\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Token Symbol *\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                lineNumber: 590,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                id: \"symbol\",\n                                                name: \"symbol\",\n                                                value: formData.symbol,\n                                                onChange: handleInputChange,\n                                                placeholder: \"e.g., AST\",\n                                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                lineNumber: 593,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 589,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"decimals\",\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Decimals *\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                lineNumber: 606,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                id: \"decimals\",\n                                                name: \"decimals\",\n                                                value: formData.decimals,\n                                                onChange: handleInputChange,\n                                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                                required: true,\n                                                children: [\n                                                    ...Array(19)\n                                                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: i,\n                                                        children: [\n                                                            i,\n                                                            \" decimals\"\n                                                        ]\n                                                    }, i, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                        lineNumber: 618,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                lineNumber: 609,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 605,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"maxSupply\",\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Maximum Supply *\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                lineNumber: 624,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"number\",\n                                                id: \"maxSupply\",\n                                                name: \"maxSupply\",\n                                                value: formData.maxSupply,\n                                                onChange: handleInputChange,\n                                                placeholder: \"10000000\",\n                                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                lineNumber: 627,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 623,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                lineNumber: 572,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"adminAddress\",\n                                        className: \"block text-sm font-medium text-gray-700\",\n                                        children: \"Admin Address *\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 641,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        id: \"adminAddress\",\n                                        name: \"adminAddress\",\n                                        value: formData.adminAddress,\n                                        onChange: handleInputChange,\n                                        placeholder: \"0x...\",\n                                        className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 644,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                lineNumber: 640,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"tokenPrice\",\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Token Price\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                lineNumber: 658,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"number\",\n                                                step: \"0.01\",\n                                                id: \"tokenPrice\",\n                                                name: \"tokenPrice\",\n                                                value: formData.tokenPrice,\n                                                onChange: handleInputChange,\n                                                placeholder: \"1.00\",\n                                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                lineNumber: 661,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 657,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"currency\",\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Currency\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                lineNumber: 674,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                id: \"currency\",\n                                                name: \"currency\",\n                                                value: formData.currency,\n                                                onChange: handleInputChange,\n                                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"USD\",\n                                                        children: \"USD\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                        lineNumber: 684,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"EUR\",\n                                                        children: \"EUR\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                        lineNumber: 685,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"GBP\",\n                                                        children: \"GBP\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                        lineNumber: 686,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"JPY\",\n                                                        children: \"JPY\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                        lineNumber: 687,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"CAD\",\n                                                        children: \"CAD\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                        lineNumber: 688,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"AUD\",\n                                                        children: \"AUD\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                        lineNumber: 689,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"CHF\",\n                                                        children: \"CHF\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                        lineNumber: 690,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"CNY\",\n                                                        children: \"CNY\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                        lineNumber: 691,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"ETH\",\n                                                        children: \"ETH\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                        lineNumber: 692,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"BTC\",\n                                                        children: \"BTC\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                        lineNumber: 693,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"USDC\",\n                                                        children: \"USDC\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                        lineNumber: 694,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"USDT\",\n                                                        children: \"USDT\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                        lineNumber: 695,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                lineNumber: 677,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 673,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"tokenImageUrl\",\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Token Image URL\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                lineNumber: 700,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"url\",\n                                                id: \"tokenImageUrl\",\n                                                name: \"tokenImageUrl\",\n                                                value: formData.tokenImageUrl,\n                                                onChange: handleInputChange,\n                                                placeholder: \"https://...\",\n                                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                lineNumber: 703,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 699,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                lineNumber: 656,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"bonusTiers\",\n                                        className: \"block text-sm font-medium text-gray-700\",\n                                        children: \"Bonus Tiers\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 716,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        id: \"bonusTiers\",\n                                        name: \"bonusTiers\",\n                                        value: formData.bonusTiers,\n                                        onChange: handleInputChange,\n                                        rows: 2,\n                                        placeholder: \"Early Bird: 10%, Standard: 5%, Late: 0%\",\n                                        className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 719,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                lineNumber: 715,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"tokenDetails\",\n                                        className: \"block text-sm font-medium text-gray-700\",\n                                        children: \"Token Details\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 731,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        id: \"tokenDetails\",\n                                        name: \"tokenDetails\",\n                                        value: formData.tokenDetails,\n                                        onChange: handleInputChange,\n                                        rows: 3,\n                                        placeholder: \"Describe your security token...\",\n                                        className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 734,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                lineNumber: 730,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pt-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: isSubmitting,\n                                    className: \"w-full bg-purple-600 hover:bg-purple-700 text-white font-bold py-3 px-4 rounded disabled:opacity-50\",\n                                    children: isSubmitting ? '🔄 Creating Modular Token...' : '🚀 Create Modular Token'\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                    lineNumber: 746,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                lineNumber: 745,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                        lineNumber: 571,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                lineNumber: 568,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6 p-4 bg-green-50 border border-green-200 rounded-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-shrink-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-green-600 text-xl\",\n                                children: \"✅\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                lineNumber: 762,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                            lineNumber: 761,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"ml-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-sm font-medium text-green-800\",\n                                    children: \"Production Ready\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                    lineNumber: 765,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-1 text-sm text-green-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"This interface deploys real modular security tokens using our factory contract. Each token is a new proxy instance with full upgrade capabilities.\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                            lineNumber: 769,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1\",\n                                            children: \"All tokens created here are fully functional and ready for production use on Amoy testnet.\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                            lineNumber: 770,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                    lineNumber: 768,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                            lineNumber: 764,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                    lineNumber: 760,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                lineNumber: 759,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n        lineNumber: 427,\n        columnNumber: 5\n    }, this);\n}\n_s(CreateModularTokenPage, \"B48lQD7Qppt66Qd7SYrVcUCLgGM=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = CreateModularTokenPage;\nvar _c;\n$RefreshReg$(_c, \"CreateModularTokenPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvY3JlYXRlLW1vZHVsYXItdG9rZW4vcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7O0FBRW1EO0FBQ25CO0FBQ0g7QUFDZTtBQUU1QyxzQ0FBc0M7QUFDdEMsTUFBTU0sOEJBQThCQyw0Q0FBd0Q7QUFDNUYsTUFBTUcsMEJBQTBCSCw0Q0FBb0Q7QUFDcEYsTUFBTUssZ0NBQWdDTCw0Q0FBMEQ7QUFzQmpGLFNBQVNPOztJQUN0QixNQUFNQyxTQUFTViwwREFBU0E7SUFFeEIsbUJBQW1CO0lBQ25CLE1BQU0sQ0FBQ1csVUFBVUMsWUFBWSxHQUFHaEIsK0NBQVFBLENBQWdDO0lBQ3hFLE1BQU0sQ0FBQ2lCLFFBQVFDLFVBQVUsR0FBR2xCLCtDQUFRQSxDQUE4QjtJQUNsRSxNQUFNLENBQUNtQixjQUFjQyxnQkFBZ0IsR0FBR3BCLCtDQUFRQSxDQUFDO0lBQ2pELE1BQU0sQ0FBQ3FCLE9BQU9DLFNBQVMsR0FBR3RCLCtDQUFRQSxDQUFnQjtJQUNsRCxNQUFNLENBQUN1QixTQUFTQyxXQUFXLEdBQUd4QiwrQ0FBUUEsQ0FBZ0I7SUFDdEQsTUFBTSxDQUFDeUIsZUFBZUMsaUJBQWlCLEdBQUcxQiwrQ0FBUUEsQ0FBdUI7SUFDekUsTUFBTSxDQUFDMkIsaUJBQWlCQyxtQkFBbUIsR0FBRzVCLCtDQUFRQSxDQUFpQjtJQUN2RSxNQUFNLENBQUM2QixnQkFBZ0JDLGtCQUFrQixHQUFHOUIsK0NBQVFBLENBQUM7SUFFckQsTUFBTSxDQUFDK0IsVUFBVUMsWUFBWSxHQUFHaEMsK0NBQVFBLENBQWdCO1FBQ3REaUMsTUFBTTtRQUNOQyxRQUFRO1FBQ1JDLFVBQVU7UUFDVkMsV0FBVztRQUNYQyxjQUFjO1FBQ2RDLFlBQVk7UUFDWkMsVUFBVTtRQUNWQyxZQUFZO1FBQ1pDLGNBQWM7UUFDZEMsZUFBZTtJQUNqQjtJQUVBekMsZ0RBQVNBOzRDQUFDO1lBQ1IwQztRQUNGOzJDQUFHLEVBQUU7SUFFTDFDLGdEQUFTQTs0Q0FBQztZQUNSLElBQUlnQixVQUFVTiwrQkFBK0I7Z0JBQzNDaUM7WUFDRjtRQUNGOzJDQUFHO1FBQUMzQjtRQUFRTjtLQUE4QjtJQUUxQyxNQUFNZ0MscUJBQXFCO1FBQ3pCLElBQUk7WUFDRixJQUFJLEtBQTZCLElBQUlFLE9BQU9DLFFBQVEsRUFBRTtnQkFDcEQsTUFBTS9CLFdBQVcsSUFBSWIsbURBQXNCLENBQUMyQyxPQUFPQyxRQUFRO2dCQUMzRCxNQUFNL0IsU0FBU2lDLElBQUksQ0FBQyx1QkFBdUIsRUFBRTtnQkFFN0MsZ0JBQWdCO2dCQUNoQixNQUFNQyxVQUFVLE1BQU1sQyxTQUFTbUMsVUFBVTtnQkFDekNDLFFBQVFDLEdBQUcsQ0FBQyx5QkFBeUJILFFBQVFoQixJQUFJLEVBQUUsYUFBYWdCLFFBQVFJLE9BQU8sQ0FBQ0MsUUFBUTtnQkFFeEYsaUNBQWlDO2dCQUNqQyxJQUFJTCxRQUFRSSxPQUFPLEtBQUssTUFBTSxFQUFFO29CQUM5Qi9CLFNBQVMsaUZBQTRHLE9BQTNCMkIsUUFBUUksT0FBTyxDQUFDQyxRQUFRO29CQUNsSDtnQkFDRjtnQkFFQSxNQUFNckMsU0FBUyxNQUFNRixTQUFTd0MsU0FBUztnQkFDdkMsTUFBTUMsVUFBVSxNQUFNdkMsT0FBT3dDLFVBQVU7Z0JBRXZDLGdCQUFnQjtnQkFDaEIsTUFBTUMsVUFBVSxNQUFNM0MsU0FBUzRDLFVBQVUsQ0FBQ0g7Z0JBQzFDTCxRQUFRQyxHQUFHLENBQUMsbUJBQW1CbEQsK0NBQWtCLENBQUN3RCxVQUFVO2dCQUU1RCxJQUFJQSxVQUFVeEQsOENBQWlCLENBQUMsU0FBUztvQkFDdkNvQixTQUFTO29CQUNUO2dCQUNGO2dCQUVBTixZQUFZRDtnQkFDWkcsVUFBVUQ7Z0JBQ1ZlLFlBQVk4QixDQUFBQSxPQUFTO3dCQUFFLEdBQUdBLElBQUk7d0JBQUV6QixjQUFjbUI7b0JBQVE7WUFDeEQsT0FBTztnQkFDTGxDLFNBQVM7WUFDWDtRQUNGLEVBQUUsT0FBT0QsT0FBTztZQUNkOEIsUUFBUTlCLEtBQUssQ0FBQyxnQ0FBZ0NBO1lBQzlDQyxTQUFTO1FBQ1g7SUFDRjtJQUVBLE1BQU1zQixvQkFBb0I7UUFDeEIsSUFBSSxDQUFDM0IsVUFBVSxDQUFDTiwrQkFBK0I7UUFFL0MsSUFBSTtZQUNGLE1BQU1vRCx5QkFBeUIsTUFBTSw0UUFBa0Q7WUFDdkYsTUFBTUMsVUFBVSxJQUFJOUQsNENBQWUsQ0FDakNTLCtCQUNBb0QsdUJBQXVCRyxPQUFPLEVBQzlCakQ7WUFHRixNQUFNa0QsZ0JBQWdCLE1BQU1ILFFBQVFHLGFBQWE7WUFDakQsTUFBTUMsY0FBYyxNQUFNbkQsT0FBT3dDLFVBQVU7WUFDM0MsTUFBTVksVUFBVSxNQUFNTCxRQUFRSyxPQUFPLENBQUNGLGVBQWVDO1lBRXJEeEMsbUJBQW1CeUM7UUFDckIsRUFBRSxPQUFPaEQsT0FBTztZQUNkOEIsUUFBUTlCLEtBQUssQ0FBQyxpQ0FBaUNBO1lBQy9DTyxtQkFBbUI7UUFDckI7SUFDRjtJQUVBLE1BQU0wQyxzQkFBc0I7UUFDMUIsSUFBSSxDQUFDckQsVUFBVSxDQUFDTiwrQkFBK0I7UUFFL0NtQixrQkFBa0I7UUFDbEJSLFNBQVM7UUFFVCxJQUFJO1lBQ0YsTUFBTXlDLHlCQUF5QixNQUFNLDRRQUFrRDtZQUN2RixNQUFNQyxVQUFVLElBQUk5RCw0Q0FBZSxDQUNqQ1MsK0JBQ0FvRCx1QkFBdUJHLE9BQU8sRUFDOUJqRDtZQUdGLE1BQU1rRCxnQkFBZ0IsTUFBTUgsUUFBUUcsYUFBYTtZQUNqRCxNQUFNQyxjQUFjLE1BQU1uRCxPQUFPd0MsVUFBVTtZQUUzQyx5RUFBeUU7WUFDekUsTUFBTWMsS0FBSyxNQUFNUCxRQUFRUSxTQUFTLENBQUNMLGVBQWVDO1lBQ2xELE1BQU1HLEdBQUdFLElBQUk7WUFFYjdDLG1CQUFtQjtZQUNuQkosV0FBVztRQUNiLEVBQUUsT0FBT0gsT0FBWTtZQUNuQjhCLFFBQVE5QixLQUFLLENBQUMsaUNBQWlDQTtZQUMvQyxJQUFJQSxNQUFNcUQsT0FBTyxDQUFDQyxRQUFRLENBQUMscUNBQXFDO2dCQUM5RHJELFNBQVM7WUFDWCxPQUFPO2dCQUNMQSxTQUFTLGtDQUFnRCxPQUFkRCxNQUFNcUQsT0FBTztZQUMxRDtRQUNGLFNBQVU7WUFDUjVDLGtCQUFrQjtRQUNwQjtJQUNGO0lBRUEsTUFBTThDLG9CQUFvQixDQUFDQztRQUN6QixNQUFNLEVBQUU1QyxJQUFJLEVBQUU2QyxLQUFLLEVBQUVDLElBQUksRUFBRSxHQUFHRixFQUFFRyxNQUFNO1FBRXRDLElBQUlDLGlCQUFzQkg7UUFFMUIsSUFBSTdDLFNBQVMsWUFBWTtZQUN2QmdELGlCQUFpQkMsU0FBU0osT0FBTztRQUNuQztRQUVBOUMsWUFBWTtZQUNWLEdBQUdELFFBQVE7WUFDWCxDQUFDRSxLQUFLLEVBQUVnRDtRQUNWO0lBQ0Y7SUFFQSxNQUFNRSxxQkFBcUI7UUFDekIsSUFBSSxDQUFDbEUsVUFBVSxDQUFDTiwrQkFBK0I7WUFDN0NXLFNBQVM7WUFDVDtRQUNGO1FBRUFGLGdCQUFnQjtRQUNoQkUsU0FBUztRQUNURSxXQUFXO1FBRVgsSUFBSTtZQUNGLHFDQUFxQztZQUNyQyxNQUFNdUMseUJBQXlCLE1BQU0sNFFBQWtEO1lBRXZGLG1DQUFtQztZQUNuQyxNQUFNQyxVQUFVLElBQUk5RCw0Q0FBZSxDQUNqQ1MsK0JBQ0FvRCx1QkFBdUJHLE9BQU8sRUFDOUJqRDtZQUdGLGtDQUFrQztZQUNsQyxJQUFJVSxvQkFBb0IsT0FBTztnQkFDN0JMLFNBQVM7Z0JBQ1Q7WUFDRjtZQUVBLDhDQUE4QztZQUM5QyxJQUFJSyxvQkFBb0IsTUFBTTtnQkFDNUIsTUFBTXdDLGdCQUFnQixNQUFNSCxRQUFRRyxhQUFhO2dCQUNqRCxNQUFNQyxjQUFjLE1BQU1uRCxPQUFPd0MsVUFBVTtnQkFDM0MsTUFBTTJCLFlBQVksTUFBTXBCLFFBQVFLLE9BQU8sQ0FBQ0YsZUFBZUM7Z0JBRXZELElBQUksQ0FBQ2dCLFdBQVc7b0JBQ2R4RCxtQkFBbUI7b0JBQ25CTixTQUFTO29CQUNUO2dCQUNGO2dCQUNBTSxtQkFBbUI7WUFDckI7WUFFQSxxQ0FBcUM7WUFDckMsTUFBTXlELGVBQWVuRiw4Q0FBaUIsQ0FBQzZCLFNBQVNLLFNBQVMsRUFBRUwsU0FBU0ksUUFBUTtZQUU1RSw2QkFBNkI7WUFDN0IsTUFBTW9ELHlCQUF5QixHQUEwQnhELE9BQXZCQSxTQUFTTyxVQUFVLEVBQUMsS0FBcUIsT0FBbEJQLFNBQVNRLFFBQVE7WUFFMUUsdUNBQXVDO1lBQ3ZDWSxRQUFRQyxHQUFHLENBQUMsZ0NBQWdDO2dCQUMxQ25CLE1BQU1GLFNBQVNFLElBQUk7Z0JBQ25CQyxRQUFRSCxTQUFTRyxNQUFNO2dCQUN2QkMsVUFBVUosU0FBU0ksUUFBUTtnQkFDM0JDLFdBQVdMLFNBQVNLLFNBQVM7Z0JBQzdCb0QsT0FBT3pELFNBQVNNLFlBQVk7Z0JBQzVCQyxZQUFZaUQ7Z0JBQ1ovQyxZQUFZVCxTQUFTUyxVQUFVO2dCQUMvQkMsY0FBY1YsU0FBU1UsWUFBWTtnQkFDbkNDLGVBQWVYLFNBQVNXLGFBQWE7WUFDdkM7WUFFQSw0QkFBNEI7WUFDNUIsSUFBSStDO1lBQ0osSUFBSUMsY0FBYztZQUVsQixJQUFJO2dCQUNGRCxjQUFjLE1BQU16QixRQUFRMkIsV0FBVyxDQUFDQyxXQUFXLENBQ2pEN0QsU0FBU0UsSUFBSSxFQUNiRixTQUFTRyxNQUFNLEVBQ2ZILFNBQVNJLFFBQVEsRUFDakJrRCxjQUNBdEQsU0FBU00sWUFBWSxFQUNyQmtELHdCQUNBeEQsU0FBU1MsVUFBVSxFQUNuQlQsU0FBU1UsWUFBWSxFQUNyQlYsU0FBU1csYUFBYTtnQkFFeEJTLFFBQVFDLEdBQUcsQ0FBQyxpQkFBaUJxQyxZQUFZbkMsUUFBUTtZQUNuRCxFQUFFLE9BQU91QyxVQUFVO2dCQUNqQjFDLFFBQVEyQyxJQUFJLENBQUMsaURBQWlERCxTQUFTbkIsT0FBTztnQkFDOUVlLGNBQWNNLE9BQU8sVUFBVSxrQ0FBa0M7Z0JBQ2pFTCxjQUFjO1lBQ2hCO1lBRUEsNENBQTRDO1lBQzVDLE1BQU1NLFdBQVdOLGNBQWNELGNBQWNBLGNBQWVBLGNBQWNNLE9BQU8sTUFBTUEsT0FBTztZQUU5RjVDLFFBQVFDLEdBQUcsQ0FBQyxvQkFBb0I0QyxTQUFTMUMsUUFBUTtZQUVqRCw2RUFBNkU7WUFDN0UsSUFBSWlCO1lBQ0osSUFBSTtnQkFDRiwyQ0FBMkM7Z0JBQzNDQSxLQUFLLE1BQU1QLFFBQVEyQixXQUFXLENBQzVCNUQsU0FBU0UsSUFBSSxFQUNiRixTQUFTRyxNQUFNLEVBQ2ZILFNBQVNJLFFBQVEsRUFDakJrRCxjQUNBdEQsU0FBU00sWUFBWSxFQUNyQmtELHdCQUNBeEQsU0FBU1MsVUFBVSxFQUNuQlQsU0FBU1UsWUFBWSxFQUNyQlYsU0FBU1csYUFBYTtZQUUxQixFQUFFLE9BQU91RCxZQUFZO2dCQUNuQjlDLFFBQVEyQyxJQUFJLENBQUMsMERBQTBERyxXQUFXdkIsT0FBTztnQkFFekYsMENBQTBDO2dCQUMxQyxNQUFNd0IsVUFBVSxNQUFNakYsT0FBT0YsUUFBUSxDQUFDb0YsVUFBVTtnQkFDaEQsTUFBTUMsWUFBWTtvQkFDaEJKLFVBQVVBO29CQUNWSyxjQUFjSCxRQUFRRyxZQUFZLElBQUluRyw4Q0FBaUIsQ0FBQyxNQUFNO29CQUM5RG9HLHNCQUFzQkosUUFBUUksb0JBQW9CLElBQUlwRyw4Q0FBaUIsQ0FBQyxLQUFLO2dCQUMvRTtnQkFFQWlELFFBQVFDLEdBQUcsQ0FBQyxtQ0FBbUM7b0JBQzdDNEMsVUFBVUksVUFBVUosUUFBUSxDQUFDMUMsUUFBUTtvQkFDckMrQyxjQUFjbkcsK0NBQWtCLENBQUNrRyxVQUFVQyxZQUFZLEVBQUUsVUFBVTtvQkFDbkVDLHNCQUFzQnBHLCtDQUFrQixDQUFDa0csVUFBVUUsb0JBQW9CLEVBQUUsVUFBVTtnQkFDckY7Z0JBRUEvQixLQUFLLE1BQU1QLFFBQVEyQixXQUFXLENBQzVCNUQsU0FBU0UsSUFBSSxFQUNiRixTQUFTRyxNQUFNLEVBQ2ZILFNBQVNJLFFBQVEsRUFDakJrRCxjQUNBdEQsU0FBU00sWUFBWSxFQUNyQmtELHdCQUNBeEQsU0FBU1MsVUFBVSxFQUNuQlQsU0FBU1UsWUFBWSxFQUNyQlYsU0FBU1csYUFBYSxFQUN0QjBEO1lBRUo7WUFFQWpELFFBQVFDLEdBQUcsQ0FBQyxxQkFBcUJtQixHQUFHaUMsSUFBSTtZQUN4Q2hGLFdBQVc7WUFFWCxNQUFNaUYsVUFBVSxNQUFNbEMsR0FBR0UsSUFBSTtZQUM3QnRCLFFBQVFDLEdBQUcsQ0FBQywwQkFBMEJxRDtZQUV0QyxnREFBZ0Q7WUFDaEQsTUFBTUMsUUFBUUQsUUFBUUUsSUFBSSxDQUFDQyxJQUFJLENBQUMsQ0FBQ3hEO2dCQUMvQixJQUFJO29CQUNGLE1BQU15RCxTQUFTN0MsUUFBUThDLFNBQVMsQ0FBQ0MsUUFBUSxDQUFDM0Q7b0JBQzFDLE9BQU95RCxPQUFPNUUsSUFBSSxLQUFLO2dCQUN6QixFQUFFLFVBQU07b0JBQ04sT0FBTztnQkFDVDtZQUNGO1lBRUEsSUFBSXlFLE9BQU87Z0JBQ1QsTUFBTU0sY0FBY2hELFFBQVE4QyxTQUFTLENBQUNDLFFBQVEsQ0FBQ0w7Z0JBQy9DLE1BQU1PLGVBQWVELFlBQVlFLElBQUksQ0FBQ0QsWUFBWTtnQkFFbER2RixpQkFBaUI7b0JBQ2Y4QixTQUFTeUQ7b0JBQ1RoRixNQUFNRixTQUFTRSxJQUFJO29CQUNuQkMsUUFBUUgsU0FBU0csTUFBTTtvQkFDdkJpRixpQkFBaUI1QyxHQUFHaUMsSUFBSTtnQkFDMUI7Z0JBRUFoRixXQUFXLGlEQUE4RCxPQUFieUY7WUFDOUQsT0FBTztnQkFDTDNGLFNBQVM7WUFDWDtRQUVGLEVBQUUsT0FBT0QsT0FBWTtnQkFNbUJBLHNCQUFBQTtZQUx0QzhCLFFBQVE5QixLQUFLLENBQUMsa0NBQWtDQTtZQUVoRCw4QkFBOEI7WUFDOUIsSUFBSStGLGVBQWUvRixNQUFNcUQsT0FBTztZQUVoQyxJQUFJckQsTUFBTWdHLElBQUksS0FBSyxxQkFBbUJoRyxlQUFBQSxNQUFNQSxLQUFLLGNBQVhBLG9DQUFBQSx1QkFBQUEsYUFBYXFELE9BQU8sY0FBcEJyRCwyQ0FBQUEscUJBQXNCc0QsUUFBUSxDQUFDLDZCQUE0QjtnQkFDL0Z5QyxlQUFlO1lBQ2pCLE9BQU8sSUFBSS9GLE1BQU1xRCxPQUFPLENBQUNDLFFBQVEsQ0FBQyxxQ0FBcUM7Z0JBQ3JFeUMsZUFBZTtZQUNqQixPQUFPLElBQUkvRixNQUFNcUQsT0FBTyxDQUFDQyxRQUFRLENBQUMsdUJBQXVCO2dCQUN2RHlDLGVBQWU7WUFDakIsT0FBTyxJQUFJL0YsTUFBTXFELE9BQU8sQ0FBQ0MsUUFBUSxDQUFDLFFBQVE7Z0JBQ3hDeUMsZUFBZTtZQUNqQixPQUFPLElBQUkvRixNQUFNcUQsT0FBTyxDQUFDQyxRQUFRLENBQUMsVUFBVXRELE1BQU1xRCxPQUFPLENBQUNDLFFBQVEsQ0FBQyxRQUFRO2dCQUN6RXlDLGVBQWU7WUFDakIsT0FBTyxJQUFJL0YsTUFBTWdHLElBQUksS0FBSyxpQkFBaUI7Z0JBQ3pDRCxlQUFlO1lBQ2pCLE9BQU8sSUFBSS9GLE1BQU1nRyxJQUFJLEtBQUssV0FBVztnQkFDbkNELGVBQWU7WUFDakI7WUFFQTlGLFNBQVMsbUNBQWdELE9BQWI4RjtZQUU1QyxnQ0FBZ0M7WUFDaENqRSxRQUFRQyxHQUFHLENBQUMsa0JBQWtCO2dCQUM1QmlFLE1BQU1oRyxNQUFNZ0csSUFBSTtnQkFDaEJDLFFBQVFqRyxNQUFNaUcsTUFBTTtnQkFDcEJDLE1BQU1sRyxNQUFNa0csSUFBSTtnQkFDaEJDLGFBQWFuRyxNQUFNbUcsV0FBVztZQUNoQztRQUNGLFNBQVU7WUFDUnBHLGdCQUFnQjtRQUNsQjtJQUNGO0lBRUEsTUFBTXFHLGVBQWUsT0FBTzVDO1FBQzFCQSxFQUFFNkMsY0FBYztRQUVoQixhQUFhO1FBQ2IsSUFBSSxDQUFDM0YsU0FBU0UsSUFBSSxJQUFJLENBQUNGLFNBQVNHLE1BQU0sSUFBSSxDQUFDSCxTQUFTTSxZQUFZLEVBQUU7WUFDaEVmLFNBQVM7WUFDVDtRQUNGO1FBRUEsSUFBSVMsU0FBU0ksUUFBUSxHQUFHLEtBQUtKLFNBQVNJLFFBQVEsR0FBRyxJQUFJO1lBQ25EYixTQUFTO1lBQ1Q7UUFDRjtRQUVBLE1BQU02RDtJQUNSO0lBRUEsSUFBSSxDQUFDcEUsWUFBWSxDQUFDRSxRQUFRO1FBQ3hCLHFCQUNFLDhEQUFDMEc7OzhCQUNDLDhEQUFDQTtvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUN6SCxrREFBSUE7NEJBQUMwSCxNQUFLOzRCQUFJRCxXQUFVO3NDQUF5Qzs7Ozs7O3NDQUdsRSw4REFBQ0U7NEJBQUdGLFdBQVU7c0NBQXFCOzs7Ozs7Ozs7Ozs7OEJBR3JDLDhEQUFDRDtvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUNHOzRCQUFHSCxXQUFVO3NDQUF5Qjs7Ozs7O3NDQUN2Qyw4REFBQ0k7NEJBQUVKLFdBQVU7c0NBQXFCOzs7Ozs7c0NBR2xDLDhEQUFDSzs0QkFDQ0MsU0FBU3ZGOzRCQUNUaUYsV0FBVTtzQ0FDWDs7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBTVQ7SUFFQSxxQkFDRSw4REFBQ0Q7OzBCQUNDLDhEQUFDQTtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUN6SCxrREFBSUE7d0JBQUMwSCxNQUFLO3dCQUFJRCxXQUFVO2tDQUF5Qzs7Ozs7O2tDQUdsRSw4REFBQ0U7d0JBQUdGLFdBQVU7a0NBQXFCOzs7Ozs7a0NBQ25DLDhEQUFDTzt3QkFBS1AsV0FBVTtrQ0FBZ0Y7Ozs7Ozs7Ozs7OzswQkFNbEcsOERBQUNEO2dCQUFJQyxXQUFVOzBCQUNiLDRFQUFDRDtvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUNEOzRCQUFJQyxXQUFVO3NDQUNiLDRFQUFDTztnQ0FBS1AsV0FBVTswQ0FBMEI7Ozs7Ozs7Ozs7O3NDQUU1Qyw4REFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDUTtvQ0FBR1IsV0FBVTs4Q0FBc0M7Ozs7Ozs4Q0FHcEQsOERBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ0k7c0RBQUU7Ozs7OztzREFDSCw4REFBQ0s7NENBQUdULFdBQVU7OzhEQUNaLDhEQUFDVTs7c0VBQUcsOERBQUNDO3NFQUFPOzs7Ozs7d0RBQStCOzs7Ozs7OzhEQUMzQyw4REFBQ0Q7O3NFQUFHLDhEQUFDQztzRUFBTzs7Ozs7O3dEQUF3Qjs7Ozs7Ozs4REFDcEMsOERBQUNEOztzRUFBRyw4REFBQ0M7c0VBQU87Ozs7Ozt3REFBMkI7Ozs7Ozs7OERBQ3ZDLDhEQUFDRDs7c0VBQUcsOERBQUNDO3NFQUFPOzs7Ozs7d0RBQTBCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBUWhELDhEQUFDWjtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNRO3dCQUFHUixXQUFVO2tDQUF5Qzs7Ozs7O2tDQUN2RCw4REFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDRDs7a0RBQ0MsOERBQUNRO3dDQUFLUCxXQUFVO2tEQUFjOzs7Ozs7a0RBQzlCLDhEQUFDTzt3Q0FBS1AsV0FBVTtrREFBMEJqSDs7Ozs7Ozs7Ozs7OzBDQUU1Qyw4REFBQ2dIOztrREFDQyw4REFBQ1E7d0NBQUtQLFdBQVU7a0RBQWM7Ozs7OztrREFDOUIsOERBQUNPO3dDQUFLUCxXQUFVO2tEQUEwQnZIOzs7Ozs7Ozs7Ozs7MENBRTVDLDhEQUFDc0g7O2tEQUNDLDhEQUFDUTt3Q0FBS1AsV0FBVTtrREFBYzs7Ozs7O2tEQUM5Qiw4REFBQ087d0NBQUtQLFdBQVU7a0RBQTBCbkg7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFNaEQsOERBQUNrSDtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNRO3dCQUFHUixXQUFVO2tDQUF5Qzs7Ozs7O2tDQUN2RCw4REFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNPO3dDQUFLUCxXQUFVO2tEQUF3Qjs7Ozs7O29DQUN2Q2pHLG9CQUFvQixxQkFDbkIsOERBQUN3Rzt3Q0FBS1AsV0FBVTtrREFBa0I7Ozs7OytDQUNoQ2pHLGdDQUNGLDhEQUFDd0c7d0NBQUtQLFdBQVU7a0RBQWlCOzs7Ozs2REFFakMsOERBQUNPO3dDQUFLUCxXQUFVO2tEQUFlOzs7Ozs7Ozs7Ozs7NEJBR2xDakcsb0JBQW9CLHVCQUNuQiw4REFBQ3NHO2dDQUNDQyxTQUFTNUQ7Z0NBQ1RrRSxVQUFVM0c7Z0NBQ1YrRixXQUFVOzBDQUVUL0YsaUJBQWlCLG9CQUFvQjs7Ozs7Ozs7Ozs7O29CQUkzQ0Ysb0JBQW9CLHVCQUNuQiw4REFBQ3FHO3dCQUFFSixXQUFVO2tDQUE2Qjs7Ozs7Ozs7Ozs7O1lBTzdDdkcsdUJBQ0MsOERBQUNzRztnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDTzs0QkFBS1AsV0FBVTtzQ0FBb0I7Ozs7OztzQ0FDcEMsOERBQUNPO3NDQUFNOUc7Ozs7Ozs7Ozs7Ozs7Ozs7O1lBTVpFLHlCQUNDLDhEQUFDb0c7Z0JBQUlDLFdBQVU7MEJBQ2IsNEVBQUNEO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ087NEJBQUtQLFdBQVU7c0NBQXNCOzs7Ozs7c0NBQ3RDLDhEQUFDTztzQ0FBTTVHOzs7Ozs7Ozs7Ozs7Ozs7OztZQU1aRSwrQkFDQyw4REFBQ2tHO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ1E7d0JBQUdSLFdBQVU7a0NBQTBDOzs7Ozs7a0NBQ3hELDhEQUFDRDt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ087d0NBQUtQLFdBQVU7a0RBQWM7Ozs7OztrREFDOUIsOERBQUNPO2tEQUFNMUcsY0FBY1EsSUFBSTs7Ozs7Ozs7Ozs7OzBDQUUzQiw4REFBQzBGO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ087d0NBQUtQLFdBQVU7a0RBQWM7Ozs7OztrREFDOUIsOERBQUNPO2tEQUFNMUcsY0FBY1MsTUFBTTs7Ozs7Ozs7Ozs7OzBDQUU3Qiw4REFBQ3lGO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ087d0NBQUtQLFdBQVU7a0RBQWM7Ozs7OztrREFDOUIsOERBQUNPO3dDQUFLUCxXQUFVO2tEQUFxQm5HLGNBQWMrQixPQUFPOzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBRzlELDhEQUFDbUU7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDSztnQ0FDQ0MsU0FBUyxJQUFNckYsT0FBTzRGLElBQUksQ0FBQyx3Q0FBOEQsT0FBdEJoSCxjQUFjK0IsT0FBTyxHQUFJO2dDQUM1Rm9FLFdBQVU7MENBQ1g7Ozs7OzswQ0FHRCw4REFBQ3pILGtEQUFJQTtnQ0FDSDBILE1BQUs7Z0NBQ0xELFdBQVU7MENBQ1g7Ozs7Ozs7Ozs7Ozs7Ozs7OztZQVFOLENBQUNuRywrQkFDQSw4REFBQ2tHO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ0c7d0JBQUdILFdBQVU7a0NBQXlCOzs7Ozs7a0NBRXZDLDhEQUFDYzt3QkFBS0MsVUFBVWxCO3dCQUFjRyxXQUFVOzswQ0FDdEMsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0Q7OzBEQUNDLDhEQUFDaUI7Z0RBQU1DLFNBQVE7Z0RBQU9qQixXQUFVOzBEQUEwQzs7Ozs7OzBEQUcxRSw4REFBQ2tCO2dEQUNDL0QsTUFBSztnREFDTGdFLElBQUc7Z0RBQ0g5RyxNQUFLO2dEQUNMNkMsT0FBTy9DLFNBQVNFLElBQUk7Z0RBQ3BCK0csVUFBVXBFO2dEQUNWcUUsYUFBWTtnREFDWnJCLFdBQVU7Z0RBQ1ZzQixRQUFROzs7Ozs7Ozs7Ozs7a0RBSVosOERBQUN2Qjs7MERBQ0MsOERBQUNpQjtnREFBTUMsU0FBUTtnREFBU2pCLFdBQVU7MERBQTBDOzs7Ozs7MERBRzVFLDhEQUFDa0I7Z0RBQ0MvRCxNQUFLO2dEQUNMZ0UsSUFBRztnREFDSDlHLE1BQUs7Z0RBQ0w2QyxPQUFPL0MsU0FBU0csTUFBTTtnREFDdEI4RyxVQUFVcEU7Z0RBQ1ZxRSxhQUFZO2dEQUNackIsV0FBVTtnREFDVnNCLFFBQVE7Ozs7Ozs7Ozs7OztrREFJWiw4REFBQ3ZCOzswREFDQyw4REFBQ2lCO2dEQUFNQyxTQUFRO2dEQUFXakIsV0FBVTswREFBMEM7Ozs7OzswREFHOUUsOERBQUN1QjtnREFDQ0osSUFBRztnREFDSDlHLE1BQUs7Z0RBQ0w2QyxPQUFPL0MsU0FBU0ksUUFBUTtnREFDeEI2RyxVQUFVcEU7Z0RBQ1ZnRCxXQUFVO2dEQUNWc0IsUUFBUTswREFFUDt1REFBSUUsTUFBTTtpREFBSSxDQUFDQyxHQUFHLENBQUMsQ0FBQ0MsR0FBR0Msa0JBQ3RCLDhEQUFDQzt3REFBZTFFLE9BQU95RTs7NERBQUlBOzREQUFFOzt1REFBaEJBOzs7Ozs7Ozs7Ozs7Ozs7O2tEQUtuQiw4REFBQzVCOzswREFDQyw4REFBQ2lCO2dEQUFNQyxTQUFRO2dEQUFZakIsV0FBVTswREFBMEM7Ozs7OzswREFHL0UsOERBQUNrQjtnREFDQy9ELE1BQUs7Z0RBQ0xnRSxJQUFHO2dEQUNIOUcsTUFBSztnREFDTDZDLE9BQU8vQyxTQUFTSyxTQUFTO2dEQUN6QjRHLFVBQVVwRTtnREFDVnFFLGFBQVk7Z0RBQ1pyQixXQUFVO2dEQUNWc0IsUUFBUTs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQUtkLDhEQUFDdkI7O2tEQUNDLDhEQUFDaUI7d0NBQU1DLFNBQVE7d0NBQWVqQixXQUFVO2tEQUEwQzs7Ozs7O2tEQUdsRiw4REFBQ2tCO3dDQUNDL0QsTUFBSzt3Q0FDTGdFLElBQUc7d0NBQ0g5RyxNQUFLO3dDQUNMNkMsT0FBTy9DLFNBQVNNLFlBQVk7d0NBQzVCMkcsVUFBVXBFO3dDQUNWcUUsYUFBWTt3Q0FDWnJCLFdBQVU7d0NBQ1ZzQixRQUFROzs7Ozs7Ozs7Ozs7MENBSVosOERBQUN2QjtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNEOzswREFDQyw4REFBQ2lCO2dEQUFNQyxTQUFRO2dEQUFhakIsV0FBVTswREFBMEM7Ozs7OzswREFHaEYsOERBQUNrQjtnREFDQy9ELE1BQUs7Z0RBQ0wwRSxNQUFLO2dEQUNMVixJQUFHO2dEQUNIOUcsTUFBSztnREFDTDZDLE9BQU8vQyxTQUFTTyxVQUFVO2dEQUMxQjBHLFVBQVVwRTtnREFDVnFFLGFBQVk7Z0RBQ1pyQixXQUFVOzs7Ozs7Ozs7Ozs7a0RBSWQsOERBQUNEOzswREFDQyw4REFBQ2lCO2dEQUFNQyxTQUFRO2dEQUFXakIsV0FBVTswREFBMEM7Ozs7OzswREFHOUUsOERBQUN1QjtnREFDQ0osSUFBRztnREFDSDlHLE1BQUs7Z0RBQ0w2QyxPQUFPL0MsU0FBU1EsUUFBUTtnREFDeEJ5RyxVQUFVcEU7Z0RBQ1ZnRCxXQUFVOztrRUFFViw4REFBQzRCO3dEQUFPMUUsT0FBTTtrRUFBTTs7Ozs7O2tFQUNwQiw4REFBQzBFO3dEQUFPMUUsT0FBTTtrRUFBTTs7Ozs7O2tFQUNwQiw4REFBQzBFO3dEQUFPMUUsT0FBTTtrRUFBTTs7Ozs7O2tFQUNwQiw4REFBQzBFO3dEQUFPMUUsT0FBTTtrRUFBTTs7Ozs7O2tFQUNwQiw4REFBQzBFO3dEQUFPMUUsT0FBTTtrRUFBTTs7Ozs7O2tFQUNwQiw4REFBQzBFO3dEQUFPMUUsT0FBTTtrRUFBTTs7Ozs7O2tFQUNwQiw4REFBQzBFO3dEQUFPMUUsT0FBTTtrRUFBTTs7Ozs7O2tFQUNwQiw4REFBQzBFO3dEQUFPMUUsT0FBTTtrRUFBTTs7Ozs7O2tFQUNwQiw4REFBQzBFO3dEQUFPMUUsT0FBTTtrRUFBTTs7Ozs7O2tFQUNwQiw4REFBQzBFO3dEQUFPMUUsT0FBTTtrRUFBTTs7Ozs7O2tFQUNwQiw4REFBQzBFO3dEQUFPMUUsT0FBTTtrRUFBTzs7Ozs7O2tFQUNyQiw4REFBQzBFO3dEQUFPMUUsT0FBTTtrRUFBTzs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tEQUl6Qiw4REFBQzZDOzswREFDQyw4REFBQ2lCO2dEQUFNQyxTQUFRO2dEQUFnQmpCLFdBQVU7MERBQTBDOzs7Ozs7MERBR25GLDhEQUFDa0I7Z0RBQ0MvRCxNQUFLO2dEQUNMZ0UsSUFBRztnREFDSDlHLE1BQUs7Z0RBQ0w2QyxPQUFPL0MsU0FBU1csYUFBYTtnREFDN0JzRyxVQUFVcEU7Z0RBQ1ZxRSxhQUFZO2dEQUNackIsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQUtoQiw4REFBQ0Q7O2tEQUNDLDhEQUFDaUI7d0NBQU1DLFNBQVE7d0NBQWFqQixXQUFVO2tEQUEwQzs7Ozs7O2tEQUdoRiw4REFBQzhCO3dDQUNDWCxJQUFHO3dDQUNIOUcsTUFBSzt3Q0FDTDZDLE9BQU8vQyxTQUFTUyxVQUFVO3dDQUMxQndHLFVBQVVwRTt3Q0FDVitFLE1BQU07d0NBQ05WLGFBQVk7d0NBQ1pyQixXQUFVOzs7Ozs7Ozs7Ozs7MENBSWQsOERBQUNEOztrREFDQyw4REFBQ2lCO3dDQUFNQyxTQUFRO3dDQUFlakIsV0FBVTtrREFBMEM7Ozs7OztrREFHbEYsOERBQUM4Qjt3Q0FDQ1gsSUFBRzt3Q0FDSDlHLE1BQUs7d0NBQ0w2QyxPQUFPL0MsU0FBU1UsWUFBWTt3Q0FDNUJ1RyxVQUFVcEU7d0NBQ1YrRSxNQUFNO3dDQUNOVixhQUFZO3dDQUNackIsV0FBVTs7Ozs7Ozs7Ozs7OzBDQUlkLDhEQUFDRDtnQ0FBSUMsV0FBVTswQ0FDYiw0RUFBQ0s7b0NBQ0NsRCxNQUFLO29DQUNMeUQsVUFBVXJIO29DQUNWeUcsV0FBVTs4Q0FFVHpHLGVBQWUsaUNBQWlDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFRM0QsOERBQUN3RztnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDRDs0QkFBSUMsV0FBVTtzQ0FDYiw0RUFBQ087Z0NBQUtQLFdBQVU7MENBQXlCOzs7Ozs7Ozs7OztzQ0FFM0MsOERBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ1E7b0NBQUdSLFdBQVU7OENBQXFDOzs7Ozs7OENBR25ELDhEQUFDRDtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNJO3NEQUFFOzs7Ozs7c0RBQ0gsOERBQUNBOzRDQUFFSixXQUFVO3NEQUFPOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU9sQztHQXh1QndCL0c7O1FBQ1BULHNEQUFTQTs7O0tBREZTIiwic291cmNlcyI6WyJEOlxcZ2l0aHViXFx0b2tlbmRldi1uZXdyb29cXGFkbWluLXBhbmVsXFxzcmNcXGFwcFxcY3JlYXRlLW1vZHVsYXItdG9rZW5cXHBhZ2UudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IFJlYWN0LCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBldGhlcnMgfSBmcm9tICdldGhlcnMnO1xuaW1wb3J0IExpbmsgZnJvbSAnbmV4dC9saW5rJztcbmltcG9ydCB7IHVzZVJvdXRlciB9IGZyb20gJ25leHQvbmF2aWdhdGlvbic7XG5cbi8vIENvbnRyYWN0IGFkZHJlc3NlcyBmcm9tIGVudmlyb25tZW50XG5jb25zdCBTRUNVUklUWV9UT0tFTl9DT1JFX0FERFJFU1MgPSBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19BTU9ZX1NFQ1VSSVRZX1RPS0VOX0NPUkVfQUREUkVTUztcbmNvbnN0IFVQR1JBREVfTUFOQUdFUl9BRERSRVNTID0gcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfQU1PWV9VUEdSQURFX01BTkFHRVJfQUREUkVTUztcbmNvbnN0IE1PRFVMQVJfVE9LRU5fRkFDVE9SWV9BRERSRVNTID0gcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfQU1PWV9NT0RVTEFSX1RPS0VOX0ZBQ1RPUllfQUREUkVTUztcblxuaW50ZXJmYWNlIFRva2VuRm9ybURhdGEge1xuICBuYW1lOiBzdHJpbmc7XG4gIHN5bWJvbDogc3RyaW5nO1xuICBkZWNpbWFsczogbnVtYmVyO1xuICBtYXhTdXBwbHk6IHN0cmluZztcbiAgYWRtaW5BZGRyZXNzOiBzdHJpbmc7XG4gIHRva2VuUHJpY2U6IHN0cmluZztcbiAgY3VycmVuY3k6IHN0cmluZztcbiAgYm9udXNUaWVyczogc3RyaW5nO1xuICB0b2tlbkRldGFpbHM6IHN0cmluZztcbiAgdG9rZW5JbWFnZVVybDogc3RyaW5nO1xufVxuXG5pbnRlcmZhY2UgRGVwbG95ZWRUb2tlbiB7XG4gIGFkZHJlc3M6IHN0cmluZztcbiAgbmFtZTogc3RyaW5nO1xuICBzeW1ib2w6IHN0cmluZztcbiAgdHJhbnNhY3Rpb25IYXNoOiBzdHJpbmc7XG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIENyZWF0ZU1vZHVsYXJUb2tlblBhZ2UoKSB7XG4gIGNvbnN0IHJvdXRlciA9IHVzZVJvdXRlcigpO1xuICBcbiAgLy8gU3RhdGUgTWFuYWdlbWVudFxuICBjb25zdCBbcHJvdmlkZXIsIHNldFByb3ZpZGVyXSA9IHVzZVN0YXRlPGV0aGVycy5Ccm93c2VyUHJvdmlkZXIgfCBudWxsPihudWxsKTtcbiAgY29uc3QgW3NpZ25lciwgc2V0U2lnbmVyXSA9IHVzZVN0YXRlPGV0aGVycy5Kc29uUnBjU2lnbmVyIHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IFtpc1N1Ym1pdHRpbmcsIHNldElzU3VibWl0dGluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtlcnJvciwgc2V0RXJyb3JdID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IFtzdWNjZXNzLCBzZXRTdWNjZXNzXSA9IHVzZVN0YXRlPHN0cmluZyB8IG51bGw+KG51bGwpO1xuICBjb25zdCBbZGVwbG95ZWRUb2tlbiwgc2V0RGVwbG95ZWRUb2tlbl0gPSB1c2VTdGF0ZTxEZXBsb3llZFRva2VuIHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IFtoYXNEZXBsb3llclJvbGUsIHNldEhhc0RlcGxveWVyUm9sZV0gPSB1c2VTdGF0ZTxib29sZWFuIHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IFtpc0NoZWNraW5nUm9sZSwgc2V0SXNDaGVja2luZ1JvbGVdID0gdXNlU3RhdGUoZmFsc2UpO1xuXG4gIGNvbnN0IFtmb3JtRGF0YSwgc2V0Rm9ybURhdGFdID0gdXNlU3RhdGU8VG9rZW5Gb3JtRGF0YT4oe1xuICAgIG5hbWU6ICcnLFxuICAgIHN5bWJvbDogJycsXG4gICAgZGVjaW1hbHM6IDAsXG4gICAgbWF4U3VwcGx5OiAnMTAwMDAwMDAnLFxuICAgIGFkbWluQWRkcmVzczogJycsXG4gICAgdG9rZW5QcmljZTogJzEuMDAnLFxuICAgIGN1cnJlbmN5OiAnVVNEJyxcbiAgICBib251c1RpZXJzOiAnRWFybHkgQmlyZDogMTAlLCBTdGFuZGFyZDogNSUsIExhdGU6IDAlJyxcbiAgICB0b2tlbkRldGFpbHM6ICdTZWN1cml0eSB0b2tlbiB3aXRoIGFkdmFuY2VkIGNvbXBsaWFuY2UgZmVhdHVyZXMnLFxuICAgIHRva2VuSW1hZ2VVcmw6ICcnXG4gIH0pO1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaW5pdGlhbGl6ZVByb3ZpZGVyKCk7XG4gIH0sIFtdKTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmIChzaWduZXIgJiYgTU9EVUxBUl9UT0tFTl9GQUNUT1JZX0FERFJFU1MpIHtcbiAgICAgIGNoZWNrRGVwbG95ZXJSb2xlKCk7XG4gICAgfVxuICB9LCBbc2lnbmVyLCBNT0RVTEFSX1RPS0VOX0ZBQ1RPUllfQUREUkVTU10pO1xuXG4gIGNvbnN0IGluaXRpYWxpemVQcm92aWRlciA9IGFzeW5jICgpID0+IHtcbiAgICB0cnkge1xuICAgICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnICYmIHdpbmRvdy5ldGhlcmV1bSkge1xuICAgICAgICBjb25zdCBwcm92aWRlciA9IG5ldyBldGhlcnMuQnJvd3NlclByb3ZpZGVyKHdpbmRvdy5ldGhlcmV1bSk7XG4gICAgICAgIGF3YWl0IHByb3ZpZGVyLnNlbmQoJ2V0aF9yZXF1ZXN0QWNjb3VudHMnLCBbXSk7XG5cbiAgICAgICAgLy8gQ2hlY2sgbmV0d29ya1xuICAgICAgICBjb25zdCBuZXR3b3JrID0gYXdhaXQgcHJvdmlkZXIuZ2V0TmV0d29yaygpO1xuICAgICAgICBjb25zb2xlLmxvZygnQ29ubmVjdGVkIHRvIG5ldHdvcms6JywgbmV0d29yay5uYW1lLCAnQ2hhaW4gSUQ6JywgbmV0d29yay5jaGFpbklkLnRvU3RyaW5nKCkpO1xuXG4gICAgICAgIC8vIEFtb3kgdGVzdG5ldCBjaGFpbiBJRCBpcyA4MDAwMlxuICAgICAgICBpZiAobmV0d29yay5jaGFpbklkICE9PSA4MDAwMm4pIHtcbiAgICAgICAgICBzZXRFcnJvcihgV3JvbmcgbmV0d29yayEgUGxlYXNlIHN3aXRjaCB0byBBbW95IHRlc3RuZXQgKENoYWluIElEOiA4MDAwMikuIEN1cnJlbnRseSBvbjogJHtuZXR3b3JrLmNoYWluSWQudG9TdHJpbmcoKX1gKTtcbiAgICAgICAgICByZXR1cm47XG4gICAgICAgIH1cblxuICAgICAgICBjb25zdCBzaWduZXIgPSBhd2FpdCBwcm92aWRlci5nZXRTaWduZXIoKTtcbiAgICAgICAgY29uc3QgYWRkcmVzcyA9IGF3YWl0IHNpZ25lci5nZXRBZGRyZXNzKCk7XG5cbiAgICAgICAgLy8gQ2hlY2sgYmFsYW5jZVxuICAgICAgICBjb25zdCBiYWxhbmNlID0gYXdhaXQgcHJvdmlkZXIuZ2V0QmFsYW5jZShhZGRyZXNzKTtcbiAgICAgICAgY29uc29sZS5sb2coJ1dhbGxldCBiYWxhbmNlOicsIGV0aGVycy5mb3JtYXRFdGhlcihiYWxhbmNlKSwgJ0VUSCcpO1xuXG4gICAgICAgIGlmIChiYWxhbmNlIDwgZXRoZXJzLnBhcnNlRXRoZXIoJzAuMDEnKSkge1xuICAgICAgICAgIHNldEVycm9yKCdJbnN1ZmZpY2llbnQgYmFsYW5jZS4gWW91IG5lZWQgYXQgbGVhc3QgMC4wMSBFVEggZm9yIGdhcyBmZWVzLicpO1xuICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuXG4gICAgICAgIHNldFByb3ZpZGVyKHByb3ZpZGVyKTtcbiAgICAgICAgc2V0U2lnbmVyKHNpZ25lcik7XG4gICAgICAgIHNldEZvcm1EYXRhKHByZXYgPT4gKHsgLi4ucHJldiwgYWRtaW5BZGRyZXNzOiBhZGRyZXNzIH0pKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHNldEVycm9yKCdNZXRhTWFzayBub3QgZm91bmQuIFBsZWFzZSBpbnN0YWxsIE1ldGFNYXNrIHRvIGNyZWF0ZSB0b2tlbnMuJyk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGluaXRpYWxpemluZyBwcm92aWRlcjonLCBlcnJvcik7XG4gICAgICBzZXRFcnJvcignRmFpbGVkIHRvIGNvbm5lY3QgdG8gd2FsbGV0Jyk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGNoZWNrRGVwbG95ZXJSb2xlID0gYXN5bmMgKCkgPT4ge1xuICAgIGlmICghc2lnbmVyIHx8ICFNT0RVTEFSX1RPS0VOX0ZBQ1RPUllfQUREUkVTUykgcmV0dXJuO1xuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IE1vZHVsYXJUb2tlbkZhY3RvcnlBQkkgPSBhd2FpdCBpbXBvcnQoJy4uLy4uL2NvbnRyYWN0cy9Nb2R1bGFyVG9rZW5GYWN0b3J5Lmpzb24nKTtcbiAgICAgIGNvbnN0IGZhY3RvcnkgPSBuZXcgZXRoZXJzLkNvbnRyYWN0KFxuICAgICAgICBNT0RVTEFSX1RPS0VOX0ZBQ1RPUllfQUREUkVTUyxcbiAgICAgICAgTW9kdWxhclRva2VuRmFjdG9yeUFCSS5kZWZhdWx0LFxuICAgICAgICBzaWduZXJcbiAgICAgICk7XG5cbiAgICAgIGNvbnN0IERFUExPWUVSX1JPTEUgPSBhd2FpdCBmYWN0b3J5LkRFUExPWUVSX1JPTEUoKTtcbiAgICAgIGNvbnN0IHVzZXJBZGRyZXNzID0gYXdhaXQgc2lnbmVyLmdldEFkZHJlc3MoKTtcbiAgICAgIGNvbnN0IGhhc1JvbGUgPSBhd2FpdCBmYWN0b3J5Lmhhc1JvbGUoREVQTE9ZRVJfUk9MRSwgdXNlckFkZHJlc3MpO1xuXG4gICAgICBzZXRIYXNEZXBsb3llclJvbGUoaGFzUm9sZSk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGNoZWNraW5nIGRlcGxveWVyIHJvbGU6JywgZXJyb3IpO1xuICAgICAgc2V0SGFzRGVwbG95ZXJSb2xlKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgcmVxdWVzdERlcGxveWVyUm9sZSA9IGFzeW5jICgpID0+IHtcbiAgICBpZiAoIXNpZ25lciB8fCAhTU9EVUxBUl9UT0tFTl9GQUNUT1JZX0FERFJFU1MpIHJldHVybjtcblxuICAgIHNldElzQ2hlY2tpbmdSb2xlKHRydWUpO1xuICAgIHNldEVycm9yKG51bGwpO1xuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IE1vZHVsYXJUb2tlbkZhY3RvcnlBQkkgPSBhd2FpdCBpbXBvcnQoJy4uLy4uL2NvbnRyYWN0cy9Nb2R1bGFyVG9rZW5GYWN0b3J5Lmpzb24nKTtcbiAgICAgIGNvbnN0IGZhY3RvcnkgPSBuZXcgZXRoZXJzLkNvbnRyYWN0KFxuICAgICAgICBNT0RVTEFSX1RPS0VOX0ZBQ1RPUllfQUREUkVTUyxcbiAgICAgICAgTW9kdWxhclRva2VuRmFjdG9yeUFCSS5kZWZhdWx0LFxuICAgICAgICBzaWduZXJcbiAgICAgICk7XG5cbiAgICAgIGNvbnN0IERFUExPWUVSX1JPTEUgPSBhd2FpdCBmYWN0b3J5LkRFUExPWUVSX1JPTEUoKTtcbiAgICAgIGNvbnN0IHVzZXJBZGRyZXNzID0gYXdhaXQgc2lnbmVyLmdldEFkZHJlc3MoKTtcblxuICAgICAgLy8gVHJ5IHRvIGdyYW50IHRoZSByb2xlICh0aGlzIHdpbGwgb25seSB3b3JrIGlmIHRoZSB1c2VyIGhhcyBhZG1pbiByb2xlKVxuICAgICAgY29uc3QgdHggPSBhd2FpdCBmYWN0b3J5LmdyYW50Um9sZShERVBMT1lFUl9ST0xFLCB1c2VyQWRkcmVzcyk7XG4gICAgICBhd2FpdCB0eC53YWl0KCk7XG5cbiAgICAgIHNldEhhc0RlcGxveWVyUm9sZSh0cnVlKTtcbiAgICAgIHNldFN1Y2Nlc3MoJ0RlcGxveWVyIHJvbGUgZ3JhbnRlZCBzdWNjZXNzZnVsbHkhJyk7XG4gICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZ3JhbnRpbmcgZGVwbG95ZXIgcm9sZTonLCBlcnJvcik7XG4gICAgICBpZiAoZXJyb3IubWVzc2FnZS5pbmNsdWRlcygnQWNjZXNzQ29udHJvbFVuYXV0aG9yaXplZEFjY291bnQnKSkge1xuICAgICAgICBzZXRFcnJvcignWW91IGRvIG5vdCBoYXZlIHBlcm1pc3Npb24gdG8gZ3JhbnQgcm9sZXMuIFBsZWFzZSBjb250YWN0IGFuIGFkbWluaXN0cmF0b3IgdG8gZ3JhbnQgeW91IHRoZSBERVBMT1lFUl9ST0xFLicpO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgc2V0RXJyb3IoYEZhaWxlZCB0byBncmFudCBkZXBsb3llciByb2xlOiAke2Vycm9yLm1lc3NhZ2V9YCk7XG4gICAgICB9XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldElzQ2hlY2tpbmdSb2xlKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlSW5wdXRDaGFuZ2UgPSAoZTogUmVhY3QuQ2hhbmdlRXZlbnQ8SFRNTElucHV0RWxlbWVudCB8IEhUTUxUZXh0QXJlYUVsZW1lbnQgfCBIVE1MU2VsZWN0RWxlbWVudD4pID0+IHtcbiAgICBjb25zdCB7IG5hbWUsIHZhbHVlLCB0eXBlIH0gPSBlLnRhcmdldCBhcyBIVE1MSW5wdXRFbGVtZW50O1xuICAgIFxuICAgIGxldCBwcm9jZXNzZWRWYWx1ZTogYW55ID0gdmFsdWU7XG4gICAgXG4gICAgaWYgKG5hbWUgPT09ICdkZWNpbWFscycpIHtcbiAgICAgIHByb2Nlc3NlZFZhbHVlID0gcGFyc2VJbnQodmFsdWUsIDEwKTtcbiAgICB9XG4gICAgXG4gICAgc2V0Rm9ybURhdGEoe1xuICAgICAgLi4uZm9ybURhdGEsXG4gICAgICBbbmFtZV06IHByb2Nlc3NlZFZhbHVlXG4gICAgfSk7XG4gIH07XG5cbiAgY29uc3QgZGVwbG95TW9kdWxhclRva2VuID0gYXN5bmMgKCkgPT4ge1xuICAgIGlmICghc2lnbmVyIHx8ICFNT0RVTEFSX1RPS0VOX0ZBQ1RPUllfQUREUkVTUykge1xuICAgICAgc2V0RXJyb3IoJ1BsZWFzZSBjb25uZWN0IHlvdXIgd2FsbGV0IGZpcnN0Jyk7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgc2V0SXNTdWJtaXR0aW5nKHRydWUpO1xuICAgIHNldEVycm9yKG51bGwpO1xuICAgIHNldFN1Y2Nlc3MobnVsbCk7XG5cbiAgICB0cnkge1xuICAgICAgLy8gSW1wb3J0IHRoZSBNb2R1bGFyVG9rZW5GYWN0b3J5IEFCSVxuICAgICAgY29uc3QgTW9kdWxhclRva2VuRmFjdG9yeUFCSSA9IGF3YWl0IGltcG9ydCgnLi4vLi4vY29udHJhY3RzL01vZHVsYXJUb2tlbkZhY3RvcnkuanNvbicpO1xuXG4gICAgICAvLyBDcmVhdGUgZmFjdG9yeSBjb250cmFjdCBpbnN0YW5jZVxuICAgICAgY29uc3QgZmFjdG9yeSA9IG5ldyBldGhlcnMuQ29udHJhY3QoXG4gICAgICAgIE1PRFVMQVJfVE9LRU5fRkFDVE9SWV9BRERSRVNTLFxuICAgICAgICBNb2R1bGFyVG9rZW5GYWN0b3J5QUJJLmRlZmF1bHQsXG4gICAgICAgIHNpZ25lclxuICAgICAgKTtcblxuICAgICAgLy8gQ2hlY2sgaWYgdXNlciBoYXMgREVQTE9ZRVJfUk9MRVxuICAgICAgaWYgKGhhc0RlcGxveWVyUm9sZSA9PT0gZmFsc2UpIHtcbiAgICAgICAgc2V0RXJyb3IoJ1lvdSBkbyBub3QgaGF2ZSBwZXJtaXNzaW9uIHRvIGRlcGxveSB0b2tlbnMuIFBsZWFzZSBjb250YWN0IGFuIGFkbWluaXN0cmF0b3IgdG8gZ3JhbnQgeW91IHRoZSBERVBMT1lFUl9ST0xFLicpO1xuICAgICAgICByZXR1cm47XG4gICAgICB9XG5cbiAgICAgIC8vIERvdWJsZS1jaGVjayByb2xlIGlmIHdlIGhhdmVuJ3QgY2hlY2tlZCB5ZXRcbiAgICAgIGlmIChoYXNEZXBsb3llclJvbGUgPT09IG51bGwpIHtcbiAgICAgICAgY29uc3QgREVQTE9ZRVJfUk9MRSA9IGF3YWl0IGZhY3RvcnkuREVQTE9ZRVJfUk9MRSgpO1xuICAgICAgICBjb25zdCB1c2VyQWRkcmVzcyA9IGF3YWl0IHNpZ25lci5nZXRBZGRyZXNzKCk7XG4gICAgICAgIGNvbnN0IHJvbGVDaGVjayA9IGF3YWl0IGZhY3RvcnkuaGFzUm9sZShERVBMT1lFUl9ST0xFLCB1c2VyQWRkcmVzcyk7XG5cbiAgICAgICAgaWYgKCFyb2xlQ2hlY2spIHtcbiAgICAgICAgICBzZXRIYXNEZXBsb3llclJvbGUoZmFsc2UpO1xuICAgICAgICAgIHNldEVycm9yKCdZb3UgZG8gbm90IGhhdmUgcGVybWlzc2lvbiB0byBkZXBsb3kgdG9rZW5zLiBQbGVhc2UgY29udGFjdCBhbiBhZG1pbmlzdHJhdG9yIHRvIGdyYW50IHlvdSB0aGUgREVQTE9ZRVJfUk9MRS4nKTtcbiAgICAgICAgICByZXR1cm47XG4gICAgICAgIH1cbiAgICAgICAgc2V0SGFzRGVwbG95ZXJSb2xlKHRydWUpO1xuICAgICAgfVxuXG4gICAgICAvLyBDb252ZXJ0IG1heCBzdXBwbHkgdG8gcHJvcGVyIHVuaXRzXG4gICAgICBjb25zdCBtYXhTdXBwbHlXZWkgPSBldGhlcnMucGFyc2VVbml0cyhmb3JtRGF0YS5tYXhTdXBwbHksIGZvcm1EYXRhLmRlY2ltYWxzKTtcblxuICAgICAgLy8gQ29tYmluZSBwcmljZSBhbmQgY3VycmVuY3lcbiAgICAgIGNvbnN0IHRva2VuUHJpY2VXaXRoQ3VycmVuY3kgPSBgJHtmb3JtRGF0YS50b2tlblByaWNlfSAke2Zvcm1EYXRhLmN1cnJlbmN5fWA7XG5cbiAgICAgIC8vIERlcGxveSB0aGUgdG9rZW4gdGhyb3VnaCB0aGUgZmFjdG9yeVxuICAgICAgY29uc29sZS5sb2coJ0RlcGxveWluZyB0b2tlbiB3aXRoIHBhcmFtczonLCB7XG4gICAgICAgIG5hbWU6IGZvcm1EYXRhLm5hbWUsXG4gICAgICAgIHN5bWJvbDogZm9ybURhdGEuc3ltYm9sLFxuICAgICAgICBkZWNpbWFsczogZm9ybURhdGEuZGVjaW1hbHMsXG4gICAgICAgIG1heFN1cHBseTogZm9ybURhdGEubWF4U3VwcGx5LFxuICAgICAgICBhZG1pbjogZm9ybURhdGEuYWRtaW5BZGRyZXNzLFxuICAgICAgICB0b2tlblByaWNlOiB0b2tlblByaWNlV2l0aEN1cnJlbmN5LFxuICAgICAgICBib251c1RpZXJzOiBmb3JtRGF0YS5ib251c1RpZXJzLFxuICAgICAgICB0b2tlbkRldGFpbHM6IGZvcm1EYXRhLnRva2VuRGV0YWlscyxcbiAgICAgICAgdG9rZW5JbWFnZVVybDogZm9ybURhdGEudG9rZW5JbWFnZVVybFxuICAgICAgfSk7XG5cbiAgICAgIC8vIEZpcnN0IHRyeSB0byBlc3RpbWF0ZSBnYXNcbiAgICAgIGxldCBnYXNFc3RpbWF0ZTtcbiAgICAgIGxldCB1c2VGaXhlZEdhcyA9IGZhbHNlO1xuXG4gICAgICB0cnkge1xuICAgICAgICBnYXNFc3RpbWF0ZSA9IGF3YWl0IGZhY3RvcnkuZGVwbG95VG9rZW4uZXN0aW1hdGVHYXMoXG4gICAgICAgICAgZm9ybURhdGEubmFtZSxcbiAgICAgICAgICBmb3JtRGF0YS5zeW1ib2wsXG4gICAgICAgICAgZm9ybURhdGEuZGVjaW1hbHMsXG4gICAgICAgICAgbWF4U3VwcGx5V2VpLFxuICAgICAgICAgIGZvcm1EYXRhLmFkbWluQWRkcmVzcyxcbiAgICAgICAgICB0b2tlblByaWNlV2l0aEN1cnJlbmN5LFxuICAgICAgICAgIGZvcm1EYXRhLmJvbnVzVGllcnMsXG4gICAgICAgICAgZm9ybURhdGEudG9rZW5EZXRhaWxzLFxuICAgICAgICAgIGZvcm1EYXRhLnRva2VuSW1hZ2VVcmxcbiAgICAgICAgKTtcbiAgICAgICAgY29uc29sZS5sb2coJ0dhcyBlc3RpbWF0ZTonLCBnYXNFc3RpbWF0ZS50b1N0cmluZygpKTtcbiAgICAgIH0gY2F0Y2ggKGdhc0Vycm9yKSB7XG4gICAgICAgIGNvbnNvbGUud2FybignR2FzIGVzdGltYXRpb24gZmFpbGVkLCB1c2luZyBmaXhlZCBnYXMgbGltaXQ6JywgZ2FzRXJyb3IubWVzc2FnZSk7XG4gICAgICAgIGdhc0VzdGltYXRlID0gQmlnSW50KDEwMDAwMDApOyAvLyAxTSBnYXMgYXMgY29uc2VydmF0aXZlIGZhbGxiYWNrXG4gICAgICAgIHVzZUZpeGVkR2FzID0gdHJ1ZTtcbiAgICAgIH1cblxuICAgICAgLy8gQWRkIDUwJSBidWZmZXIgdG8gZ2FzIGVzdGltYXRlIGZvciBzYWZldHlcbiAgICAgIGNvbnN0IGdhc0xpbWl0ID0gdXNlRml4ZWRHYXMgPyBnYXNFc3RpbWF0ZSA6IGdhc0VzdGltYXRlICsgKGdhc0VzdGltYXRlICogQmlnSW50KDUwKSAvIEJpZ0ludCgxMDApKTtcblxuICAgICAgY29uc29sZS5sb2coJ1VzaW5nIGdhcyBsaW1pdDonLCBnYXNMaW1pdC50b1N0cmluZygpKTtcblxuICAgICAgLy8gVHJ5IGRlcGxveW1lbnQgd2l0aCBtaW5pbWFsIHRyYW5zYWN0aW9uIG9wdGlvbnMgdG8gbGV0IE1ldGFNYXNrIGhhbmRsZSBnYXNcbiAgICAgIGxldCB0eDtcbiAgICAgIHRyeSB7XG4gICAgICAgIC8vIEZpcnN0IGF0dGVtcHQ6IExldCBNZXRhTWFzayBlc3RpbWF0ZSBnYXNcbiAgICAgICAgdHggPSBhd2FpdCBmYWN0b3J5LmRlcGxveVRva2VuKFxuICAgICAgICAgIGZvcm1EYXRhLm5hbWUsXG4gICAgICAgICAgZm9ybURhdGEuc3ltYm9sLFxuICAgICAgICAgIGZvcm1EYXRhLmRlY2ltYWxzLFxuICAgICAgICAgIG1heFN1cHBseVdlaSxcbiAgICAgICAgICBmb3JtRGF0YS5hZG1pbkFkZHJlc3MsXG4gICAgICAgICAgdG9rZW5QcmljZVdpdGhDdXJyZW5jeSxcbiAgICAgICAgICBmb3JtRGF0YS5ib251c1RpZXJzLFxuICAgICAgICAgIGZvcm1EYXRhLnRva2VuRGV0YWlscyxcbiAgICAgICAgICBmb3JtRGF0YS50b2tlbkltYWdlVXJsXG4gICAgICAgICk7XG4gICAgICB9IGNhdGNoIChmaXJzdEVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUud2FybignRmlyc3QgYXR0ZW1wdCBmYWlsZWQsIHRyeWluZyB3aXRoIG1hbnVhbCBnYXMgc2V0dGluZ3M6JywgZmlyc3RFcnJvci5tZXNzYWdlKTtcblxuICAgICAgICAvLyBTZWNvbmQgYXR0ZW1wdDogVXNlIG1hbnVhbCBnYXMgc2V0dGluZ3NcbiAgICAgICAgY29uc3QgZmVlRGF0YSA9IGF3YWl0IHNpZ25lci5wcm92aWRlci5nZXRGZWVEYXRhKCk7XG4gICAgICAgIGNvbnN0IHR4T3B0aW9ucyA9IHtcbiAgICAgICAgICBnYXNMaW1pdDogZ2FzTGltaXQsXG4gICAgICAgICAgbWF4RmVlUGVyR2FzOiBmZWVEYXRhLm1heEZlZVBlckdhcyB8fCBldGhlcnMucGFyc2VVbml0cygnNjAnLCAnZ3dlaScpLFxuICAgICAgICAgIG1heFByaW9yaXR5RmVlUGVyR2FzOiBmZWVEYXRhLm1heFByaW9yaXR5RmVlUGVyR2FzIHx8IGV0aGVycy5wYXJzZVVuaXRzKCcyJywgJ2d3ZWknKVxuICAgICAgICB9O1xuXG4gICAgICAgIGNvbnNvbGUubG9nKCdSZXRyeSB3aXRoIHRyYW5zYWN0aW9uIG9wdGlvbnM6Jywge1xuICAgICAgICAgIGdhc0xpbWl0OiB0eE9wdGlvbnMuZ2FzTGltaXQudG9TdHJpbmcoKSxcbiAgICAgICAgICBtYXhGZWVQZXJHYXM6IGV0aGVycy5mb3JtYXRVbml0cyh0eE9wdGlvbnMubWF4RmVlUGVyR2FzLCAnZ3dlaScpICsgJyBnd2VpJyxcbiAgICAgICAgICBtYXhQcmlvcml0eUZlZVBlckdhczogZXRoZXJzLmZvcm1hdFVuaXRzKHR4T3B0aW9ucy5tYXhQcmlvcml0eUZlZVBlckdhcywgJ2d3ZWknKSArICcgZ3dlaSdcbiAgICAgICAgfSk7XG5cbiAgICAgICAgdHggPSBhd2FpdCBmYWN0b3J5LmRlcGxveVRva2VuKFxuICAgICAgICAgIGZvcm1EYXRhLm5hbWUsXG4gICAgICAgICAgZm9ybURhdGEuc3ltYm9sLFxuICAgICAgICAgIGZvcm1EYXRhLmRlY2ltYWxzLFxuICAgICAgICAgIG1heFN1cHBseVdlaSxcbiAgICAgICAgICBmb3JtRGF0YS5hZG1pbkFkZHJlc3MsXG4gICAgICAgICAgdG9rZW5QcmljZVdpdGhDdXJyZW5jeSxcbiAgICAgICAgICBmb3JtRGF0YS5ib251c1RpZXJzLFxuICAgICAgICAgIGZvcm1EYXRhLnRva2VuRGV0YWlscyxcbiAgICAgICAgICBmb3JtRGF0YS50b2tlbkltYWdlVXJsLFxuICAgICAgICAgIHR4T3B0aW9uc1xuICAgICAgICApO1xuICAgICAgfVxuXG4gICAgICBjb25zb2xlLmxvZygnVHJhbnNhY3Rpb24gc2VudDonLCB0eC5oYXNoKTtcbiAgICAgIHNldFN1Y2Nlc3MoJ1RyYW5zYWN0aW9uIHNlbnQhIFdhaXRpbmcgZm9yIGNvbmZpcm1hdGlvbi4uLicpO1xuXG4gICAgICBjb25zdCByZWNlaXB0ID0gYXdhaXQgdHgud2FpdCgpO1xuICAgICAgY29uc29sZS5sb2coJ1RyYW5zYWN0aW9uIGNvbmZpcm1lZDonLCByZWNlaXB0KTtcblxuICAgICAgLy8gR2V0IHRoZSBkZXBsb3llZCB0b2tlbiBhZGRyZXNzIGZyb20gdGhlIGV2ZW50XG4gICAgICBjb25zdCBldmVudCA9IHJlY2VpcHQubG9ncy5maW5kKChsb2c6IGFueSkgPT4ge1xuICAgICAgICB0cnkge1xuICAgICAgICAgIGNvbnN0IHBhcnNlZCA9IGZhY3RvcnkuaW50ZXJmYWNlLnBhcnNlTG9nKGxvZyk7XG4gICAgICAgICAgcmV0dXJuIHBhcnNlZC5uYW1lID09PSAnVG9rZW5EZXBsb3llZCc7XG4gICAgICAgIH0gY2F0Y2gge1xuICAgICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgICAgfVxuICAgICAgfSk7XG5cbiAgICAgIGlmIChldmVudCkge1xuICAgICAgICBjb25zdCBwYXJzZWRFdmVudCA9IGZhY3RvcnkuaW50ZXJmYWNlLnBhcnNlTG9nKGV2ZW50KTtcbiAgICAgICAgY29uc3QgdG9rZW5BZGRyZXNzID0gcGFyc2VkRXZlbnQuYXJncy50b2tlbkFkZHJlc3M7XG5cbiAgICAgICAgc2V0RGVwbG95ZWRUb2tlbih7XG4gICAgICAgICAgYWRkcmVzczogdG9rZW5BZGRyZXNzLFxuICAgICAgICAgIG5hbWU6IGZvcm1EYXRhLm5hbWUsXG4gICAgICAgICAgc3ltYm9sOiBmb3JtRGF0YS5zeW1ib2wsXG4gICAgICAgICAgdHJhbnNhY3Rpb25IYXNoOiB0eC5oYXNoXG4gICAgICAgIH0pO1xuXG4gICAgICAgIHNldFN1Y2Nlc3MoYE1vZHVsYXIgdG9rZW4gZGVwbG95ZWQgc3VjY2Vzc2Z1bGx5ISBBZGRyZXNzOiAke3Rva2VuQWRkcmVzc31gKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHNldEVycm9yKCdUb2tlbiBkZXBsb3llZCBidXQgY291bGQgbm90IGZpbmQgZGVwbG95bWVudCBldmVudC4gUGxlYXNlIGNoZWNrIHRoZSB0cmFuc2FjdGlvbi4nKTtcbiAgICAgIH1cblxuICAgIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGRlcGxveWluZyBtb2R1bGFyIHRva2VuOicsIGVycm9yKTtcblxuICAgICAgLy8gUGFyc2UgY29tbW9uIGVycm9yIG1lc3NhZ2VzXG4gICAgICBsZXQgZXJyb3JNZXNzYWdlID0gZXJyb3IubWVzc2FnZTtcblxuICAgICAgaWYgKGVycm9yLmNvZGUgPT09ICdVTktOT1dOX0VSUk9SJyAmJiBlcnJvci5lcnJvcj8ubWVzc2FnZT8uaW5jbHVkZXMoJ0ludGVybmFsIEpTT04tUlBDIGVycm9yJykpIHtcbiAgICAgICAgZXJyb3JNZXNzYWdlID0gJ05ldHdvcmsgZXJyb3I6IFRoZSBBbW95IHRlc3RuZXQgaXMgZXhwZXJpZW5jaW5nIGlzc3Vlcy4gUGxlYXNlIHRyeSBhZ2FpbiBpbiBhIGZldyBtaW51dGVzLic7XG4gICAgICB9IGVsc2UgaWYgKGVycm9yLm1lc3NhZ2UuaW5jbHVkZXMoJ0FjY2Vzc0NvbnRyb2xVbmF1dGhvcml6ZWRBY2NvdW50JykpIHtcbiAgICAgICAgZXJyb3JNZXNzYWdlID0gJ1lvdSBkbyBub3QgaGF2ZSBwZXJtaXNzaW9uIHRvIGRlcGxveSB0b2tlbnMuIFBsZWFzZSBjb250YWN0IGFuIGFkbWluaXN0cmF0b3IuJztcbiAgICAgIH0gZWxzZSBpZiAoZXJyb3IubWVzc2FnZS5pbmNsdWRlcygnZXhlY3V0aW9uIHJldmVydGVkJykpIHtcbiAgICAgICAgZXJyb3JNZXNzYWdlID0gJ1RyYW5zYWN0aW9uIGZhaWxlZC4gUGxlYXNlIGNoZWNrIHlvdXIgaW5wdXRzIGFuZCB0cnkgYWdhaW4uJztcbiAgICAgIH0gZWxzZSBpZiAoZXJyb3IubWVzc2FnZS5pbmNsdWRlcygnZ2FzJykpIHtcbiAgICAgICAgZXJyb3JNZXNzYWdlID0gJ0dhcyBlc3RpbWF0aW9uIGZhaWxlZC4gVGhlIG5ldHdvcmsgbWF5IGJlIGNvbmdlc3RlZC4gUGxlYXNlIHRyeSBhZ2Fpbi4nO1xuICAgICAgfSBlbHNlIGlmIChlcnJvci5tZXNzYWdlLmluY2x1ZGVzKCdmZWUnKSAmJiBlcnJvci5tZXNzYWdlLmluY2x1ZGVzKCdjYXAnKSkge1xuICAgICAgICBlcnJvck1lc3NhZ2UgPSAnVHJhbnNhY3Rpb24gZmVlIHRvbyBoaWdoLiBQbGVhc2UgdHJ5IGFnYWluIHdoZW4gbmV0d29yayBmZWVzIGFyZSBsb3dlci4nO1xuICAgICAgfSBlbHNlIGlmIChlcnJvci5jb2RlID09PSAnTkVUV09SS19FUlJPUicpIHtcbiAgICAgICAgZXJyb3JNZXNzYWdlID0gJ05ldHdvcmsgY29ubmVjdGlvbiBlcnJvci4gUGxlYXNlIGNoZWNrIHlvdXIgaW50ZXJuZXQgY29ubmVjdGlvbiBhbmQgdHJ5IGFnYWluLic7XG4gICAgICB9IGVsc2UgaWYgKGVycm9yLmNvZGUgPT09ICdUSU1FT1VUJykge1xuICAgICAgICBlcnJvck1lc3NhZ2UgPSAnVHJhbnNhY3Rpb24gdGltZW91dC4gVGhlIG5ldHdvcmsgbWF5IGJlIHNsb3cuIFBsZWFzZSB0cnkgYWdhaW4uJztcbiAgICAgIH1cblxuICAgICAgc2V0RXJyb3IoYEZhaWxlZCB0byBkZXBsb3kgbW9kdWxhciB0b2tlbjogJHtlcnJvck1lc3NhZ2V9YCk7XG5cbiAgICAgIC8vIExvZyBhZGRpdGlvbmFsIGRlYnVnZ2luZyBpbmZvXG4gICAgICBjb25zb2xlLmxvZygnRXJyb3IgZGV0YWlsczonLCB7XG4gICAgICAgIGNvZGU6IGVycm9yLmNvZGUsXG4gICAgICAgIHJlYXNvbjogZXJyb3IucmVhc29uLFxuICAgICAgICBkYXRhOiBlcnJvci5kYXRhLFxuICAgICAgICB0cmFuc2FjdGlvbjogZXJyb3IudHJhbnNhY3Rpb25cbiAgICAgIH0pO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRJc1N1Ym1pdHRpbmcoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBoYW5kbGVTdWJtaXQgPSBhc3luYyAoZTogUmVhY3QuRm9ybUV2ZW50KSA9PiB7XG4gICAgZS5wcmV2ZW50RGVmYXVsdCgpO1xuICAgIFxuICAgIC8vIFZhbGlkYXRpb25cbiAgICBpZiAoIWZvcm1EYXRhLm5hbWUgfHwgIWZvcm1EYXRhLnN5bWJvbCB8fCAhZm9ybURhdGEuYWRtaW5BZGRyZXNzKSB7XG4gICAgICBzZXRFcnJvcignUGxlYXNlIGZpbGwgaW4gYWxsIHJlcXVpcmVkIGZpZWxkcycpO1xuICAgICAgcmV0dXJuO1xuICAgIH1cbiAgICBcbiAgICBpZiAoZm9ybURhdGEuZGVjaW1hbHMgPCAwIHx8IGZvcm1EYXRhLmRlY2ltYWxzID4gMTgpIHtcbiAgICAgIHNldEVycm9yKCdEZWNpbWFscyBtdXN0IGJlIGJldHdlZW4gMCBhbmQgMTgnKTtcbiAgICAgIHJldHVybjtcbiAgICB9XG4gICAgXG4gICAgYXdhaXQgZGVwbG95TW9kdWxhclRva2VuKCk7XG4gIH07XG5cbiAgaWYgKCFwcm92aWRlciB8fCAhc2lnbmVyKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXY+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNiBmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgIDxMaW5rIGhyZWY9XCIvXCIgY2xhc3NOYW1lPVwidGV4dC1ibHVlLTYwMCBob3Zlcjp0ZXh0LWJsdWUtODAwIG1yLTRcIj5cbiAgICAgICAgICAgICZsYXJyOyBCYWNrIHRvIERhc2hib2FyZFxuICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC0zeGwgZm9udC1ib2xkXCI+Q3JlYXRlIE1vZHVsYXIgU2VjdXJpdHkgVG9rZW48L2gxPlxuICAgICAgICA8L2Rpdj5cbiAgICAgICAgXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgcm91bmRlZC1sZyBzaGFkb3cgcC02XCI+XG4gICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1ib2xkIG1iLTJcIj5Db25uZWN0IFdhbGxldDwvaDI+XG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMCBtYi00XCI+XG4gICAgICAgICAgICBQbGVhc2UgY29ubmVjdCB5b3VyIE1ldGFNYXNrIHdhbGxldCB0byBjcmVhdGUgbW9kdWxhciB0b2tlbnMuXG4gICAgICAgICAgPC9wPlxuICAgICAgICAgIDxidXR0b24gXG4gICAgICAgICAgICBvbkNsaWNrPXtpbml0aWFsaXplUHJvdmlkZXJ9XG4gICAgICAgICAgICBjbGFzc05hbWU9XCJiZy1ibHVlLTYwMCBob3ZlcjpiZy1ibHVlLTcwMCB0ZXh0LXdoaXRlIGZvbnQtYm9sZCBweS0yIHB4LTQgcm91bmRlZFwiXG4gICAgICAgICAgPlxuICAgICAgICAgICAgQ29ubmVjdCBXYWxsZXRcbiAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICApO1xuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2PlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi02IGZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgIDxMaW5rIGhyZWY9XCIvXCIgY2xhc3NOYW1lPVwidGV4dC1ibHVlLTYwMCBob3Zlcjp0ZXh0LWJsdWUtODAwIG1yLTRcIj5cbiAgICAgICAgICAmbGFycjsgQmFjayB0byBEYXNoYm9hcmRcbiAgICAgICAgPC9MaW5rPlxuICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC0zeGwgZm9udC1ib2xkXCI+Q3JlYXRlIE1vZHVsYXIgU2VjdXJpdHkgVG9rZW48L2gxPlxuICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJtbC00IHB4LTMgcHktMSB0ZXh0LXhzIGZvbnQtbWVkaXVtIHJvdW5kZWQtZnVsbCBiZy1wdXJwbGUtMTAwIHRleHQtcHVycGxlLTgwMFwiPlxuICAgICAgICAgIEFtb3kgVGVzdG5ldFxuICAgICAgICA8L3NwYW4+XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIE1vZHVsYXIgQXJjaGl0ZWN0dXJlIE5vdGljZSAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNiBwLTQgYmctcHVycGxlLTUwIGJvcmRlciBib3JkZXItcHVycGxlLTIwMCByb3VuZGVkLWxnXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1zdGFydFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC1zaHJpbmstMFwiPlxuICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1wdXJwbGUtNjAwIHRleHQteGxcIj7wn5qAPC9zcGFuPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWwtM1wiPlxuICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1wdXJwbGUtODAwXCI+XG4gICAgICAgICAgICAgIE5leHQtR2VuZXJhdGlvbiBNb2R1bGFyIEFyY2hpdGVjdHVyZVxuICAgICAgICAgICAgPC9oMz5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtMSB0ZXh0LXNtIHRleHQtcHVycGxlLTcwMFwiPlxuICAgICAgICAgICAgICA8cD5DcmVhdGUgdG9rZW5zIHVzaW5nIG91ciBhZHZhbmNlZCBtb2R1bGFyIGFyY2hpdGVjdHVyZSB3aXRoOjwvcD5cbiAgICAgICAgICAgICAgPHVsIGNsYXNzTmFtZT1cIm10LTEgbGlzdC1kaXNjIGxpc3QtaW5zaWRlIHNwYWNlLXktMVwiPlxuICAgICAgICAgICAgICAgIDxsaT48c3Ryb25nPlVwZ3JhZGVhYmxlIENvbnRyYWN0czo8L3N0cm9uZz4gRnV0dXJlLXByb29mIHdpdGggc2VjdXJlIHVwZ3JhZGUgbWVjaGFuaXNtczwvbGk+XG4gICAgICAgICAgICAgICAgPGxpPjxzdHJvbmc+TW9kdWxhciBEZXNpZ246PC9zdHJvbmc+IEFkZCBuZXcgZmVhdHVyZXMgd2l0aG91dCByZWRlcGxveW1lbnQ8L2xpPlxuICAgICAgICAgICAgICAgIDxsaT48c3Ryb25nPkVuaGFuY2VkIFNlY3VyaXR5Ojwvc3Ryb25nPiBUaW1lbG9jayBwcm90ZWN0aW9uIGFuZCBlbWVyZ2VuY3kgY29udHJvbHM8L2xpPlxuICAgICAgICAgICAgICAgIDxsaT48c3Ryb25nPkdhcyBPcHRpbWl6YXRpb246PC9zdHJvbmc+IEVmZmljaWVudCBwcm94eSBwYXR0ZXJuIGltcGxlbWVudGF0aW9uPC9saT5cbiAgICAgICAgICAgICAgPC91bD5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogQ29udHJhY3QgSW5mb3JtYXRpb24gKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTYgcC00IGJnLWJsdWUtNTAgYm9yZGVyIGJvcmRlci1ibHVlLTIwMCByb3VuZGVkLWxnXCI+XG4gICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtYmx1ZS04MDAgbWItMlwiPk1vZHVsYXIgQXJjaGl0ZWN0dXJlIENvbnRyYWN0czwvaDM+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWJsdWUtNzAwIHNwYWNlLXktMVwiPlxuICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJmb250LW1lZGl1bVwiPk1vZHVsYXJUb2tlbkZhY3Rvcnk6PC9zcGFuPlxuICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwibWwtMiBmb250LW1vbm8gdGV4dC14c1wiPntNT0RVTEFSX1RPS0VOX0ZBQ1RPUllfQUREUkVTU308L3NwYW4+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtXCI+U2VjdXJpdHlUb2tlbkNvcmUgSW1wbGVtZW50YXRpb246PC9zcGFuPlxuICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwibWwtMiBmb250LW1vbm8gdGV4dC14c1wiPntTRUNVUklUWV9UT0tFTl9DT1JFX0FERFJFU1N9PC9zcGFuPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJmb250LW1lZGl1bVwiPlVwZ3JhZGVNYW5hZ2VyOjwvc3Bhbj5cbiAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cIm1sLTIgZm9udC1tb25vIHRleHQteHNcIj57VVBHUkFERV9NQU5BR0VSX0FERFJFU1N9PC9zcGFuPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogUm9sZSBTdGF0dXMgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTYgcC00IGJnLWdyYXktNTAgYm9yZGVyIGJvcmRlci1ncmF5LTIwMCByb3VuZGVkLWxnXCI+XG4gICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS04MDAgbWItMlwiPkRlcGxveW1lbnQgUGVybWlzc2lvbnM8L2gzPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS03MDBcIj5ERVBMT1lFUl9ST0xFIFN0YXR1czo8L3NwYW4+XG4gICAgICAgICAgICB7aGFzRGVwbG95ZXJSb2xlID09PSBudWxsID8gKFxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXllbGxvdy02MDBcIj7ij7MgQ2hlY2tpbmcuLi48L3NwYW4+XG4gICAgICAgICAgICApIDogaGFzRGVwbG95ZXJSb2xlID8gKFxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyZWVuLTYwMFwiPuKchSBHcmFudGVkPC9zcGFuPlxuICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1yZWQtNjAwXCI+4p2MIE5vdCBHcmFudGVkPC9zcGFuPlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICB7aGFzRGVwbG95ZXJSb2xlID09PSBmYWxzZSAmJiAoXG4gICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgIG9uQ2xpY2s9e3JlcXVlc3REZXBsb3llclJvbGV9XG4gICAgICAgICAgICAgIGRpc2FibGVkPXtpc0NoZWNraW5nUm9sZX1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctYmx1ZS02MDAgaG92ZXI6YmctYmx1ZS03MDAgdGV4dC13aGl0ZSBmb250LW1lZGl1bSBweS0xIHB4LTMgcm91bmRlZCB0ZXh0LXNtIGRpc2FibGVkOm9wYWNpdHktNTBcIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICB7aXNDaGVja2luZ1JvbGUgPyAn4o+zIFJlcXVlc3RpbmcuLi4nIDogJ/CflJEgUmVxdWVzdCBSb2xlJ31cbiAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICl9XG4gICAgICAgIDwvZGl2PlxuICAgICAgICB7aGFzRGVwbG95ZXJSb2xlID09PSBmYWxzZSAmJiAoXG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNjAwIG10LTJcIj5cbiAgICAgICAgICAgIFlvdSBuZWVkIHRoZSBERVBMT1lFUl9ST0xFIHRvIGNyZWF0ZSB0b2tlbnMuIENvbnRhY3QgYW4gYWRtaW5pc3RyYXRvciBpZiB0aGUgcmVxdWVzdCBmYWlscy5cbiAgICAgICAgICA8L3A+XG4gICAgICAgICl9XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIEVycm9yIE5vdGlmaWNhdGlvbiAqL31cbiAgICAgIHtlcnJvciAmJiAoXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNiBiZy1yZWQtMTAwIGJvcmRlciBib3JkZXItcmVkLTQwMCB0ZXh0LXJlZC03MDAgcHgtNCBweS0zIHJvdW5kZWRcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXJlZC02MDAgbXItMlwiPuKaoO+4jzwvc3Bhbj5cbiAgICAgICAgICAgIDxzcGFuPntlcnJvcn08L3NwYW4+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgKX1cblxuICAgICAgey8qIFN1Y2Nlc3MgTm90aWZpY2F0aW9uICovfVxuICAgICAge3N1Y2Nlc3MgJiYgKFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTYgYmctZ3JlZW4tMTAwIGJvcmRlciBib3JkZXItZ3JlZW4tNDAwIHRleHQtZ3JlZW4tNzAwIHB4LTQgcHktMyByb3VuZGVkXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmVlbi02MDAgbXItMlwiPuKchTwvc3Bhbj5cbiAgICAgICAgICAgIDxzcGFuPntzdWNjZXNzfTwvc3Bhbj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICApfVxuXG4gICAgICB7LyogRGVwbG95ZWQgVG9rZW4gUmVzdWx0ICovfVxuICAgICAge2RlcGxveWVkVG9rZW4gJiYgKFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTYgYmctZ3JlZW4tNTAgYm9yZGVyIGJvcmRlci1ncmVlbi0yMDAgcm91bmRlZC1sZyBwLTZcIj5cbiAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LW1lZGl1bSB0ZXh0LWdyZWVuLTgwMCBtYi00XCI+8J+OiSBNb2R1bGFyIFRva2VuIENyZWF0ZWQgU3VjY2Vzc2Z1bGx5ITwvaDM+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTIgdGV4dC1zbVwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJmb250LW1lZGl1bVwiPlRva2VuIE5hbWU6PC9zcGFuPlxuICAgICAgICAgICAgICA8c3Bhbj57ZGVwbG95ZWRUb2tlbi5uYW1lfTwvc3Bhbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJmb250LW1lZGl1bVwiPlN5bWJvbDo8L3NwYW4+XG4gICAgICAgICAgICAgIDxzcGFuPntkZXBsb3llZFRva2VuLnN5bWJvbH08L3NwYW4+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZm9udC1tZWRpdW1cIj5Db250cmFjdCBBZGRyZXNzOjwvc3Bhbj5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZm9udC1tb25vIHRleHQteHNcIj57ZGVwbG95ZWRUb2tlbi5hZGRyZXNzfTwvc3Bhbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtNCBmbGV4IGdhcC0yXCI+XG4gICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHdpbmRvdy5vcGVuKGBodHRwczovL2Ftb3kucG9seWdvbnNjYW4uY29tL2FkZHJlc3MvJHtkZXBsb3llZFRva2VuLmFkZHJlc3N9YCwgJ19ibGFuaycpfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy1ncmVlbi02MDAgaG92ZXI6YmctZ3JlZW4tNzAwIHRleHQtd2hpdGUgZm9udC1tZWRpdW0gcHktMiBweC00IHJvdW5kZWQgdGV4dC1zbVwiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIFZpZXcgb24gUG9seWdvblNjYW5cbiAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgPExpbmtcbiAgICAgICAgICAgICAgaHJlZj1cIi9tb2R1bGFyLXRva2Vuc1wiXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLXB1cnBsZS02MDAgaG92ZXI6YmctcHVycGxlLTcwMCB0ZXh0LXdoaXRlIGZvbnQtbWVkaXVtIHB5LTIgcHgtNCByb3VuZGVkIHRleHQtc21cIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICBNYW5hZ2UgVG9rZW5cbiAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICApfVxuXG4gICAgICB7LyogVG9rZW4gQ3JlYXRpb24gRm9ybSAqL31cbiAgICAgIHshZGVwbG95ZWRUb2tlbiAmJiAoXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgcm91bmRlZC1sZyBzaGFkb3cgcC02XCI+XG4gICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1ib2xkIG1iLTRcIj5Ub2tlbiBDb25maWd1cmF0aW9uPC9oMj5cbiAgICAgICAgICBcbiAgICAgICAgICA8Zm9ybSBvblN1Ym1pdD17aGFuZGxlU3VibWl0fSBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBnYXAtNFwiPlxuICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgIDxsYWJlbCBodG1sRm9yPVwibmFtZVwiIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMFwiPlxuICAgICAgICAgICAgICAgICAgVG9rZW4gTmFtZSAqXG4gICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcbiAgICAgICAgICAgICAgICAgIGlkPVwibmFtZVwiXG4gICAgICAgICAgICAgICAgICBuYW1lPVwibmFtZVwiXG4gICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEubmFtZX1cbiAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVJbnB1dENoYW5nZX1cbiAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiZS5nLiwgQXVnbWVudCBTZWN1cml0eSBUb2tlblwiXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJtdC0xIGJsb2NrIHctZnVsbCBweC0zIHB5LTIgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkLW1kIHNoYWRvdy1zbSBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy1ibHVlLTUwMCBmb2N1czpib3JkZXItYmx1ZS01MDBcIlxuICAgICAgICAgICAgICAgICAgcmVxdWlyZWRcbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgPGxhYmVsIGh0bWxGb3I9XCJzeW1ib2xcIiBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDBcIj5cbiAgICAgICAgICAgICAgICAgIFRva2VuIFN5bWJvbCAqXG4gICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcbiAgICAgICAgICAgICAgICAgIGlkPVwic3ltYm9sXCJcbiAgICAgICAgICAgICAgICAgIG5hbWU9XCJzeW1ib2xcIlxuICAgICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLnN5bWJvbH1cbiAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVJbnB1dENoYW5nZX1cbiAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiZS5nLiwgQVNUXCJcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cIm10LTEgYmxvY2sgdy1mdWxsIHB4LTMgcHktMiBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbWQgc2hhZG93LXNtIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLWJsdWUtNTAwIGZvY3VzOmJvcmRlci1ibHVlLTUwMFwiXG4gICAgICAgICAgICAgICAgICByZXF1aXJlZFxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICA8bGFiZWwgaHRtbEZvcj1cImRlY2ltYWxzXCIgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwXCI+XG4gICAgICAgICAgICAgICAgICBEZWNpbWFscyAqXG4gICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgICA8c2VsZWN0XG4gICAgICAgICAgICAgICAgICBpZD1cImRlY2ltYWxzXCJcbiAgICAgICAgICAgICAgICAgIG5hbWU9XCJkZWNpbWFsc1wiXG4gICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEuZGVjaW1hbHN9XG4gICAgICAgICAgICAgICAgICBvbkNoYW5nZT17aGFuZGxlSW5wdXRDaGFuZ2V9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJtdC0xIGJsb2NrIHctZnVsbCBweC0zIHB5LTIgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkLW1kIHNoYWRvdy1zbSBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy1ibHVlLTUwMCBmb2N1czpib3JkZXItYmx1ZS01MDBcIlxuICAgICAgICAgICAgICAgICAgcmVxdWlyZWRcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICB7Wy4uLkFycmF5KDE5KV0ubWFwKChfLCBpKSA9PiAoXG4gICAgICAgICAgICAgICAgICAgIDxvcHRpb24ga2V5PXtpfSB2YWx1ZT17aX0+e2l9IGRlY2ltYWxzPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICA8L3NlbGVjdD5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIFxuICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgIDxsYWJlbCBodG1sRm9yPVwibWF4U3VwcGx5XCIgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwXCI+XG4gICAgICAgICAgICAgICAgICBNYXhpbXVtIFN1cHBseSAqXG4gICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgIHR5cGU9XCJudW1iZXJcIlxuICAgICAgICAgICAgICAgICAgaWQ9XCJtYXhTdXBwbHlcIlxuICAgICAgICAgICAgICAgICAgbmFtZT1cIm1heFN1cHBseVwiXG4gICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEubWF4U3VwcGx5fVxuICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9e2hhbmRsZUlucHV0Q2hhbmdlfVxuICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCIxMDAwMDAwMFwiXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJtdC0xIGJsb2NrIHctZnVsbCBweC0zIHB5LTIgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkLW1kIHNoYWRvdy1zbSBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy1ibHVlLTUwMCBmb2N1czpib3JkZXItYmx1ZS01MDBcIlxuICAgICAgICAgICAgICAgICAgcmVxdWlyZWRcbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgXG4gICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICA8bGFiZWwgaHRtbEZvcj1cImFkbWluQWRkcmVzc1wiIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMFwiPlxuICAgICAgICAgICAgICAgIEFkbWluIEFkZHJlc3MgKlxuICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgICAgaWQ9XCJhZG1pbkFkZHJlc3NcIlxuICAgICAgICAgICAgICAgIG5hbWU9XCJhZG1pbkFkZHJlc3NcIlxuICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5hZG1pbkFkZHJlc3N9XG4gICAgICAgICAgICAgICAgb25DaGFuZ2U9e2hhbmRsZUlucHV0Q2hhbmdlfVxuICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiMHguLi5cIlxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cIm10LTEgYmxvY2sgdy1mdWxsIHB4LTMgcHktMiBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbWQgc2hhZG93LXNtIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLWJsdWUtNTAwIGZvY3VzOmJvcmRlci1ibHVlLTUwMFwiXG4gICAgICAgICAgICAgICAgcmVxdWlyZWRcbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTMgZ2FwLTRcIj5cbiAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICA8bGFiZWwgaHRtbEZvcj1cInRva2VuUHJpY2VcIiBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDBcIj5cbiAgICAgICAgICAgICAgICAgIFRva2VuIFByaWNlXG4gICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgIHR5cGU9XCJudW1iZXJcIlxuICAgICAgICAgICAgICAgICAgc3RlcD1cIjAuMDFcIlxuICAgICAgICAgICAgICAgICAgaWQ9XCJ0b2tlblByaWNlXCJcbiAgICAgICAgICAgICAgICAgIG5hbWU9XCJ0b2tlblByaWNlXCJcbiAgICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS50b2tlblByaWNlfVxuICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9e2hhbmRsZUlucHV0Q2hhbmdlfVxuICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCIxLjAwXCJcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cIm10LTEgYmxvY2sgdy1mdWxsIHB4LTMgcHktMiBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbWQgc2hhZG93LXNtIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLWJsdWUtNTAwIGZvY3VzOmJvcmRlci1ibHVlLTUwMFwiXG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICA8bGFiZWwgaHRtbEZvcj1cImN1cnJlbmN5XCIgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwXCI+XG4gICAgICAgICAgICAgICAgICBDdXJyZW5jeVxuICAgICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgPHNlbGVjdFxuICAgICAgICAgICAgICAgICAgaWQ9XCJjdXJyZW5jeVwiXG4gICAgICAgICAgICAgICAgICBuYW1lPVwiY3VycmVuY3lcIlxuICAgICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLmN1cnJlbmN5fVxuICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9e2hhbmRsZUlucHV0Q2hhbmdlfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwibXQtMSBibG9jayB3LWZ1bGwgcHgtMyBweS0yIGJvcmRlciBib3JkZXItZ3JheS0zMDAgcm91bmRlZC1tZCBzaGFkb3ctc20gZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctYmx1ZS01MDAgZm9jdXM6Ym9yZGVyLWJsdWUtNTAwXCJcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiVVNEXCI+VVNEPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiRVVSXCI+RVVSPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiR0JQXCI+R0JQPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiSlBZXCI+SlBZPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiQ0FEXCI+Q0FEPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiQVVEXCI+QVVEPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiQ0hGXCI+Q0hGPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiQ05ZXCI+Q05ZPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiRVRIXCI+RVRIPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiQlRDXCI+QlRDPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiVVNEQ1wiPlVTREM8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJVU0RUXCI+VVNEVDwvb3B0aW9uPlxuICAgICAgICAgICAgICAgIDwvc2VsZWN0PlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgIDxsYWJlbCBodG1sRm9yPVwidG9rZW5JbWFnZVVybFwiIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMFwiPlxuICAgICAgICAgICAgICAgICAgVG9rZW4gSW1hZ2UgVVJMXG4gICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgIHR5cGU9XCJ1cmxcIlxuICAgICAgICAgICAgICAgICAgaWQ9XCJ0b2tlbkltYWdlVXJsXCJcbiAgICAgICAgICAgICAgICAgIG5hbWU9XCJ0b2tlbkltYWdlVXJsXCJcbiAgICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS50b2tlbkltYWdlVXJsfVxuICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9e2hhbmRsZUlucHV0Q2hhbmdlfVxuICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJodHRwczovLy4uLlwiXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJtdC0xIGJsb2NrIHctZnVsbCBweC0zIHB5LTIgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkLW1kIHNoYWRvdy1zbSBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy1ibHVlLTUwMCBmb2N1czpib3JkZXItYmx1ZS01MDBcIlxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICBcbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxsYWJlbCBodG1sRm9yPVwiYm9udXNUaWVyc1wiIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMFwiPlxuICAgICAgICAgICAgICAgIEJvbnVzIFRpZXJzXG4gICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgIDx0ZXh0YXJlYVxuICAgICAgICAgICAgICAgIGlkPVwiYm9udXNUaWVyc1wiXG4gICAgICAgICAgICAgICAgbmFtZT1cImJvbnVzVGllcnNcIlxuICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5ib251c1RpZXJzfVxuICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVJbnB1dENoYW5nZX1cbiAgICAgICAgICAgICAgICByb3dzPXsyfVxuICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiRWFybHkgQmlyZDogMTAlLCBTdGFuZGFyZDogNSUsIExhdGU6IDAlXCJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJtdC0xIGJsb2NrIHctZnVsbCBweC0zIHB5LTIgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkLW1kIHNoYWRvdy1zbSBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy1ibHVlLTUwMCBmb2N1czpib3JkZXItYmx1ZS01MDBcIlxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICBcbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxsYWJlbCBodG1sRm9yPVwidG9rZW5EZXRhaWxzXCIgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwXCI+XG4gICAgICAgICAgICAgICAgVG9rZW4gRGV0YWlsc1xuICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICA8dGV4dGFyZWFcbiAgICAgICAgICAgICAgICBpZD1cInRva2VuRGV0YWlsc1wiXG4gICAgICAgICAgICAgICAgbmFtZT1cInRva2VuRGV0YWlsc1wiXG4gICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLnRva2VuRGV0YWlsc31cbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17aGFuZGxlSW5wdXRDaGFuZ2V9XG4gICAgICAgICAgICAgICAgcm93cz17M31cbiAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkRlc2NyaWJlIHlvdXIgc2VjdXJpdHkgdG9rZW4uLi5cIlxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cIm10LTEgYmxvY2sgdy1mdWxsIHB4LTMgcHktMiBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbWQgc2hhZG93LXNtIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLWJsdWUtNTAwIGZvY3VzOmJvcmRlci1ibHVlLTUwMFwiXG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIFxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwdC00XCI+XG4gICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICB0eXBlPVwic3VibWl0XCJcbiAgICAgICAgICAgICAgICBkaXNhYmxlZD17aXNTdWJtaXR0aW5nfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBiZy1wdXJwbGUtNjAwIGhvdmVyOmJnLXB1cnBsZS03MDAgdGV4dC13aGl0ZSBmb250LWJvbGQgcHktMyBweC00IHJvdW5kZWQgZGlzYWJsZWQ6b3BhY2l0eS01MFwiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICB7aXNTdWJtaXR0aW5nID8gJ/CflIQgQ3JlYXRpbmcgTW9kdWxhciBUb2tlbi4uLicgOiAn8J+agCBDcmVhdGUgTW9kdWxhciBUb2tlbid9XG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9mb3JtPlxuICAgICAgICA8L2Rpdj5cbiAgICAgICl9XG4gICAgICBcbiAgICAgIHsvKiBQcm9kdWN0aW9uIFJlYWR5IE5vdGljZSAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtNiBwLTQgYmctZ3JlZW4tNTAgYm9yZGVyIGJvcmRlci1ncmVlbi0yMDAgcm91bmRlZC1sZ1wiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtc3RhcnRcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtc2hyaW5rLTBcIj5cbiAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JlZW4tNjAwIHRleHQteGxcIj7inIU8L3NwYW4+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtbC0zXCI+XG4gICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyZWVuLTgwMFwiPlxuICAgICAgICAgICAgICBQcm9kdWN0aW9uIFJlYWR5XG4gICAgICAgICAgICA8L2gzPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC0xIHRleHQtc20gdGV4dC1ncmVlbi03MDBcIj5cbiAgICAgICAgICAgICAgPHA+VGhpcyBpbnRlcmZhY2UgZGVwbG95cyByZWFsIG1vZHVsYXIgc2VjdXJpdHkgdG9rZW5zIHVzaW5nIG91ciBmYWN0b3J5IGNvbnRyYWN0LiBFYWNoIHRva2VuIGlzIGEgbmV3IHByb3h5IGluc3RhbmNlIHdpdGggZnVsbCB1cGdyYWRlIGNhcGFiaWxpdGllcy48L3A+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cIm10LTFcIj5BbGwgdG9rZW5zIGNyZWF0ZWQgaGVyZSBhcmUgZnVsbHkgZnVuY3Rpb25hbCBhbmQgcmVhZHkgZm9yIHByb2R1Y3Rpb24gdXNlIG9uIEFtb3kgdGVzdG5ldC48L3A+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZVN0YXRlIiwidXNlRWZmZWN0IiwiZXRoZXJzIiwiTGluayIsInVzZVJvdXRlciIsIlNFQ1VSSVRZX1RPS0VOX0NPUkVfQUREUkVTUyIsInByb2Nlc3MiLCJlbnYiLCJORVhUX1BVQkxJQ19BTU9ZX1NFQ1VSSVRZX1RPS0VOX0NPUkVfQUREUkVTUyIsIlVQR1JBREVfTUFOQUdFUl9BRERSRVNTIiwiTkVYVF9QVUJMSUNfQU1PWV9VUEdSQURFX01BTkFHRVJfQUREUkVTUyIsIk1PRFVMQVJfVE9LRU5fRkFDVE9SWV9BRERSRVNTIiwiTkVYVF9QVUJMSUNfQU1PWV9NT0RVTEFSX1RPS0VOX0ZBQ1RPUllfQUREUkVTUyIsIkNyZWF0ZU1vZHVsYXJUb2tlblBhZ2UiLCJyb3V0ZXIiLCJwcm92aWRlciIsInNldFByb3ZpZGVyIiwic2lnbmVyIiwic2V0U2lnbmVyIiwiaXNTdWJtaXR0aW5nIiwic2V0SXNTdWJtaXR0aW5nIiwiZXJyb3IiLCJzZXRFcnJvciIsInN1Y2Nlc3MiLCJzZXRTdWNjZXNzIiwiZGVwbG95ZWRUb2tlbiIsInNldERlcGxveWVkVG9rZW4iLCJoYXNEZXBsb3llclJvbGUiLCJzZXRIYXNEZXBsb3llclJvbGUiLCJpc0NoZWNraW5nUm9sZSIsInNldElzQ2hlY2tpbmdSb2xlIiwiZm9ybURhdGEiLCJzZXRGb3JtRGF0YSIsIm5hbWUiLCJzeW1ib2wiLCJkZWNpbWFscyIsIm1heFN1cHBseSIsImFkbWluQWRkcmVzcyIsInRva2VuUHJpY2UiLCJjdXJyZW5jeSIsImJvbnVzVGllcnMiLCJ0b2tlbkRldGFpbHMiLCJ0b2tlbkltYWdlVXJsIiwiaW5pdGlhbGl6ZVByb3ZpZGVyIiwiY2hlY2tEZXBsb3llclJvbGUiLCJ3aW5kb3ciLCJldGhlcmV1bSIsIkJyb3dzZXJQcm92aWRlciIsInNlbmQiLCJuZXR3b3JrIiwiZ2V0TmV0d29yayIsImNvbnNvbGUiLCJsb2ciLCJjaGFpbklkIiwidG9TdHJpbmciLCJnZXRTaWduZXIiLCJhZGRyZXNzIiwiZ2V0QWRkcmVzcyIsImJhbGFuY2UiLCJnZXRCYWxhbmNlIiwiZm9ybWF0RXRoZXIiLCJwYXJzZUV0aGVyIiwicHJldiIsIk1vZHVsYXJUb2tlbkZhY3RvcnlBQkkiLCJmYWN0b3J5IiwiQ29udHJhY3QiLCJkZWZhdWx0IiwiREVQTE9ZRVJfUk9MRSIsInVzZXJBZGRyZXNzIiwiaGFzUm9sZSIsInJlcXVlc3REZXBsb3llclJvbGUiLCJ0eCIsImdyYW50Um9sZSIsIndhaXQiLCJtZXNzYWdlIiwiaW5jbHVkZXMiLCJoYW5kbGVJbnB1dENoYW5nZSIsImUiLCJ2YWx1ZSIsInR5cGUiLCJ0YXJnZXQiLCJwcm9jZXNzZWRWYWx1ZSIsInBhcnNlSW50IiwiZGVwbG95TW9kdWxhclRva2VuIiwicm9sZUNoZWNrIiwibWF4U3VwcGx5V2VpIiwicGFyc2VVbml0cyIsInRva2VuUHJpY2VXaXRoQ3VycmVuY3kiLCJhZG1pbiIsImdhc0VzdGltYXRlIiwidXNlRml4ZWRHYXMiLCJkZXBsb3lUb2tlbiIsImVzdGltYXRlR2FzIiwiZ2FzRXJyb3IiLCJ3YXJuIiwiQmlnSW50IiwiZ2FzTGltaXQiLCJmaXJzdEVycm9yIiwiZmVlRGF0YSIsImdldEZlZURhdGEiLCJ0eE9wdGlvbnMiLCJtYXhGZWVQZXJHYXMiLCJtYXhQcmlvcml0eUZlZVBlckdhcyIsImZvcm1hdFVuaXRzIiwiaGFzaCIsInJlY2VpcHQiLCJldmVudCIsImxvZ3MiLCJmaW5kIiwicGFyc2VkIiwiaW50ZXJmYWNlIiwicGFyc2VMb2ciLCJwYXJzZWRFdmVudCIsInRva2VuQWRkcmVzcyIsImFyZ3MiLCJ0cmFuc2FjdGlvbkhhc2giLCJlcnJvck1lc3NhZ2UiLCJjb2RlIiwicmVhc29uIiwiZGF0YSIsInRyYW5zYWN0aW9uIiwiaGFuZGxlU3VibWl0IiwicHJldmVudERlZmF1bHQiLCJkaXYiLCJjbGFzc05hbWUiLCJocmVmIiwiaDEiLCJoMiIsInAiLCJidXR0b24iLCJvbkNsaWNrIiwic3BhbiIsImgzIiwidWwiLCJsaSIsInN0cm9uZyIsImRpc2FibGVkIiwib3BlbiIsImZvcm0iLCJvblN1Ym1pdCIsImxhYmVsIiwiaHRtbEZvciIsImlucHV0IiwiaWQiLCJvbkNoYW5nZSIsInBsYWNlaG9sZGVyIiwicmVxdWlyZWQiLCJzZWxlY3QiLCJBcnJheSIsIm1hcCIsIl8iLCJpIiwib3B0aW9uIiwic3RlcCIsInRleHRhcmVhIiwicm93cyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/create-modular-token/page.tsx\n"));

/***/ })

});