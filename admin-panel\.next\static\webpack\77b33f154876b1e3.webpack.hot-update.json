{"c": ["app/layout", "app/create-token/page", "webpack"], "r": [], "m": ["(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Csrc%5C%5Capp%5C%5Ccreate-token%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!", "(app-pages-browser)/./src/app/create-token/components/ClaimsSelector.tsx", "(app-pages-browser)/./src/app/create-token/components/FactoryInfo.tsx", "(app-pages-browser)/./src/app/create-token/components/NetworkBanner.tsx", "(app-pages-browser)/./src/app/create-token/components/StatusNotification.tsx", "(app-pages-browser)/./src/app/create-token/components/TokenDeployment.tsx", "(app-pages-browser)/./src/app/create-token/components/TokenForm.tsx", "(app-pages-browser)/./src/app/create-token/components/index.ts", "(app-pages-browser)/./src/app/create-token/hooks/index.ts", "(app-pages-browser)/./src/app/create-token/hooks/useERC3643Integration.ts", "(app-pages-browser)/./src/app/create-token/hooks/useTokenDeployment.ts", "(app-pages-browser)/./src/app/create-token/page.tsx"]}