"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/create-token/page",{

/***/ "(app-pages-browser)/./src/app/create-token/hooks/useTokenDeployment.ts":
/*!**********************************************************!*\
  !*** ./src/app/create-token/hooks/useTokenDeployment.ts ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useTokenDeployment: () => (/* binding */ useTokenDeployment)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/providers/provider-browser.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/contract/contract.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/utils/units.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/constants/addresses.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/address/checks.js\");\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../config */ \"(app-pages-browser)/./src/config.ts\");\n/* harmony import */ var _contracts_ModularTokenFactory_json__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../contracts/ModularTokenFactory.json */ \"(app-pages-browser)/./src/contracts/ModularTokenFactory.json\");\n/* harmony import */ var _contracts_SecurityToken_json__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../contracts/SecurityToken.json */ \"(app-pages-browser)/./src/contracts/SecurityToken.json\");\n/* harmony import */ var _useERC3643Integration__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./useERC3643Integration */ \"(app-pages-browser)/./src/app/create-token/hooks/useERC3643Integration.ts\");\n\n\n\n\n\n\n/**\r\n * Custom hook for token deployment logic\r\n *\r\n * Encapsulates all the token deployment functionality including state management,\r\n * transaction handling, and error handling\r\n */ function useTokenDeployment(network, factoryAddress, hasDeployerRole, kycSupported) {\n    // State management\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [deployedToken, setDeployedToken] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [transactionHash, setTransactionHash] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [deploymentStep, setDeploymentStep] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('idle');\n    // ERC-3643 integration\n    const { setupERC3643Compliance, isERC3643Available } = (0,_useERC3643Integration__WEBPACK_IMPORTED_MODULE_4__.useERC3643Integration)();\n    /**\r\n   * Save token data to database\r\n   */ const saveTokenToDatabase = async (deployedToken, formData, transactionHash, blockNumber, network)=>{\n        // Fetch totalSupply from the blockchain\n        let totalSupply = '0';\n        try {\n            // Create a new provider instance for blockchain calls\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_5__.BrowserProvider(window.ethereum);\n            const token = new ethers__WEBPACK_IMPORTED_MODULE_6__.Contract(deployedToken.address, _contracts_SecurityToken_json__WEBPACK_IMPORTED_MODULE_3__.abi, provider);\n            const totalSupplyRaw = await token.totalSupply();\n            totalSupply = deployedToken.decimals === 0 ? totalSupplyRaw.toString() : ethers__WEBPACK_IMPORTED_MODULE_7__.formatUnits(totalSupplyRaw, deployedToken.decimals);\n        } catch (error) {\n            console.warn('Could not fetch totalSupply from blockchain, using default 0:', error);\n        }\n        const tokenData = {\n            address: deployedToken.address,\n            transactionHash: transactionHash,\n            blockNumber: blockNumber,\n            network: network,\n            name: deployedToken.name,\n            symbol: deployedToken.symbol,\n            decimals: deployedToken.decimals,\n            maxSupply: deployedToken.maxSupply,\n            totalSupply: totalSupply,\n            tokenType: formData.tokenType,\n            tokenPrice: deployedToken.tokenPrice,\n            currency: deployedToken.currency,\n            bonusTiers: deployedToken.bonusTiers,\n            tokenImageUrl: deployedToken.tokenImageUrl,\n            whitelistAddress: deployedToken.whitelistAddress,\n            adminAddress: deployedToken.admin,\n            hasKYC: deployedToken.hasKYC,\n            selectedClaims: formData.selectedClaims,\n            isActive: true,\n            deployedBy: deployedToken.admin,\n            deploymentNotes: \"\".concat(formData.tokenType, \" token deployed via admin panel\")\n        };\n        const response = await fetch('/api/tokens', {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify(tokenData)\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(\"Database save failed: \".concat(errorData.error || 'Unknown error'));\n        }\n        return await response.json();\n    };\n    /**\r\n   * Deploy a new token with the provided form data\r\n   */ const deployToken = async (formData)=>{\n        setIsSubmitting(true);\n        setError(null);\n        setSuccess(null);\n        setDeployedToken(null);\n        setTransactionHash(null);\n        setDeploymentStep('preparing');\n        try {\n            // Validate form data\n            validateFormData(formData);\n            // Get network configuration\n            const networkConfig = (0,_config__WEBPACK_IMPORTED_MODULE_1__.getNetworkConfig)(network);\n            if (!factoryAddress) {\n                throw new Error(\"No factory address configured for network: \".concat(network));\n            }\n            if (!window.ethereum) {\n                throw new Error('Please install MetaMask to use this feature!');\n            }\n            setDeploymentStep('connecting');\n            // Get provider and signer\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_5__.BrowserProvider(window.ethereum);\n            const signer = await provider.getSigner();\n            // Verify network connection\n            await verifyNetworkConnection(provider, network);\n            // Connect to the factory contract\n            const factory = new ethers__WEBPACK_IMPORTED_MODULE_6__.Contract(factoryAddress, _contracts_ModularTokenFactory_json__WEBPACK_IMPORTED_MODULE_2__, signer);\n            console.log(\"Connected to factory at:\", factoryAddress);\n            // Verify deployer role\n            await verifyDeployerRole(factory, signer, hasDeployerRole);\n            // Verify KYC support\n            await verifyKYCSupport(factory, formData.enableKYC);\n            // Check if token symbol already exists\n            await checkTokenSymbolAvailability(factory, formData.symbol);\n            // Convert maxSupply to the appropriate unit based on decimals\n            const maxSupplyWei = formData.decimals === 0 ? BigInt(formData.maxSupply) : ethers__WEBPACK_IMPORTED_MODULE_7__.parseUnits(formData.maxSupply, formData.decimals);\n            setDeploymentStep('deploying');\n            console.log(\"Deploying token with params:\", {\n                name: formData.name,\n                symbol: formData.symbol,\n                decimals: formData.decimals,\n                maxSupply: formData.maxSupply,\n                admin: formData.ownerAddress,\n                tokenPrice: formData.tokenPrice,\n                bonusTiers: formData.bonusTiers,\n                enableKYC: formData.enableKYC\n            });\n            // Create the transaction\n            const tx = await createDeployTransaction(factory, formData, maxSupplyWei, network, kycSupported);\n            setTransactionHash(tx.hash);\n            console.log(\"Transaction hash:\", tx.hash);\n            setDeploymentStep('confirming');\n            // Wait for the transaction to be mined\n            const receipt = await tx.wait();\n            console.log(\"Transaction mined in block:\", receipt.blockNumber);\n            setDeploymentStep('fetching');\n            // Get the token address from the transaction receipt events\n            let tokenAddress;\n            const deployEvent = receipt.logs.find((log)=>{\n                try {\n                    const parsed = factory.interface.parseLog(log);\n                    return parsed && parsed.name === 'TokenDeployed';\n                } catch (e) {\n                    return false;\n                }\n            });\n            if (deployEvent) {\n                const parsed = factory.interface.parseLog(deployEvent);\n                if (parsed) {\n                    tokenAddress = parsed.args.tokenAddress;\n                }\n            }\n            if (!tokenAddress) {\n                // Fallback: get the latest deployed token\n                const deployedTokens = await factory.getDeployedTokens(0, 100);\n                tokenAddress = deployedTokens.tokens[deployedTokens.tokens.length - 1];\n            }\n            if (tokenAddress && tokenAddress !== ethers__WEBPACK_IMPORTED_MODULE_8__.ZeroAddress) {\n                // Create token deployment result\n                const deploymentResult = await getDeploymentResult(tokenAddress, provider, formData);\n                setDeployedToken(deploymentResult);\n                // Save token to database\n                try {\n                    await saveTokenToDatabase(deploymentResult, formData, tx.hash, receipt.blockNumber.toString(), network);\n                    console.log(\"Token successfully saved to database\");\n                } catch (dbError) {\n                    console.warn(\"Failed to save token to database:\", dbError);\n                // Don't fail the deployment if database save fails\n                }\n                // Setup ERC-3643 compliance if available\n                if (isERC3643Available()) {\n                    setDeploymentStep('setting_up_compliance');\n                    console.log(\"🏛️ Setting up ERC-3643 compliance...\");\n                    try {\n                        const complianceResult = await setupERC3643Compliance(tokenAddress, formData.ownerAddress, signer, {\n                            name: formData.name,\n                            symbol: formData.symbol,\n                            tokenType: formData.tokenType,\n                            country: formData.issuerCountry || 'US',\n                            selectedClaims: formData.selectedClaims\n                        });\n                        if (complianceResult.errors.length > 0) {\n                            console.warn(\"⚠️ Some ERC-3643 setup steps failed:\", complianceResult.errors);\n                        // Don't fail deployment, just warn\n                        } else {\n                            console.log(\"✅ ERC-3643 compliance setup completed successfully\");\n                        }\n                    } catch (complianceError) {\n                        console.warn(\"⚠️ ERC-3643 compliance setup failed:\", complianceError);\n                    // Don't fail deployment, just warn\n                    }\n                } else {\n                    console.log(\"ℹ️ ERC-3643 contracts not available, skipping compliance setup\");\n                }\n                setDeploymentStep('completed');\n                setSuccess('Token \"'.concat(formData.name, '\" (').concat(formData.symbol, \") successfully deployed!\"));\n            } else {\n                throw new Error(\"Token deployment failed: Could not retrieve token address\");\n            }\n        } catch (err) {\n            handleDeploymentError(err, network);\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    /**\r\n   * Validate form data before deployment\r\n   */ const validateFormData = (formData)=>{\n        if (!formData.name || !formData.symbol || !formData.maxSupply || !formData.ownerAddress) {\n            throw new Error('Please fill in all required fields');\n        }\n        if (!ethers__WEBPACK_IMPORTED_MODULE_9__.isAddress(formData.ownerAddress)) {\n            throw new Error('Invalid owner address');\n        }\n        if (formData.decimals < 0 || formData.decimals > 18) {\n            throw new Error('Decimals must be between 0 and 18');\n        }\n    };\n    /**\r\n   * Verify that the wallet is connected to the correct network\r\n   */ const verifyNetworkConnection = async (provider, network)=>{\n        const chainId = (await provider.getNetwork()).chainId;\n        if (network === 'amoy' && chainId.toString() !== '80002') {\n            throw new Error('Please connect your wallet to the Amoy network (Chain ID: 80002)');\n        } else if (network === 'polygon' && chainId.toString() !== '137') {\n            throw new Error('Please connect your wallet to the Polygon network (Chain ID: 137)');\n        }\n    };\n    /**\r\n   * Verify that the connected wallet has DEPLOYER_ROLE\r\n   */ const verifyDeployerRole = async (factory, signer, hasRole)=>{\n        if (!hasRole) {\n            const DEPLOYER_ROLE = await factory.DEPLOYER_ROLE();\n            const hasDeployerRole = await factory.hasRole(DEPLOYER_ROLE, await signer.getAddress());\n            if (!hasDeployerRole) {\n                throw new Error(\"Your wallet does not have the DEPLOYER_ROLE required to create tokens\");\n            }\n        }\n    };\n    /**\r\n   * Check if token symbol is available\r\n   */ const checkTokenSymbolAvailability = async (factory, symbol)=>{\n        try {\n            // ModularTokenFactory doesn't have getTokenAddressBySymbol\n            // We'll check by getting all deployed tokens and checking symbols\n            const deployedTokens = await factory.getDeployedTokens(0, 1000); // Get up to 1000 tokens\n            for (const tokenAddr of deployedTokens.tokens){\n                try {\n                    const token = new ethers__WEBPACK_IMPORTED_MODULE_6__.Contract(tokenAddr, [\n                        'function symbol() view returns (string)'\n                    ], factory.runner);\n                    const existingSymbol = await token.symbol();\n                    if (existingSymbol === symbol) {\n                        throw new Error('Token with symbol \"'.concat(symbol, '\" already exists at address ').concat(tokenAddr, \". Please choose a different symbol.\"));\n                    }\n                } catch (tokenErr) {\n                    // Skip tokens that can't be read (might be different contract types)\n                    if (!tokenErr.message.includes(\"already exists\")) {\n                        continue;\n                    }\n                    throw tokenErr;\n                }\n            }\n        } catch (err) {\n            if (err.message.includes(\"already exists\")) {\n                throw err; // Re-throw our custom error\n            }\n            // If it's a different error, log it but don't fail the deployment\n            console.warn(\"Could not check token symbol availability:\", err.message);\n        }\n    };\n    /**\r\n   * Verify KYC support in the factory contract\r\n   */ const verifyKYCSupport = async (factory, enableKYC)=>{\n        // ModularTokenFactory always supports basic deployment\n        // KYC functionality is handled through modules, not deployment options\n        console.log(\"ModularTokenFactory detected - KYC will be handled through modules\");\n    };\n    /**\r\n   * Create the deployment transaction with the appropriate gas settings\r\n   */ const createDeployTransaction = async (factory, formData, maxSupplyWei, network, supportsKYC)=>{\n        // ModularTokenFactory uses deployToken function\n        console.log(\"Using ModularTokenFactory.deployToken function\");\n        // Optimized gas settings for Amoy testnet\n        if (network === 'amoy') {\n            // Optimized gas settings for Amoy testnet - let the network determine gas price\n            const gasLimit = BigInt(1000000); // 1M gas should be enough\n            console.log(\"Using optimized gas settings for Amoy testnet:\");\n            console.log(\"Gas limit:\", gasLimit.toString());\n            console.log(\"Gas price: auto (network determined)\");\n            // Call ModularTokenFactory.deployToken\n            console.log(\"Calling ModularTokenFactory.deployToken\");\n            return await factory.deployToken(formData.name, formData.symbol, formData.decimals, maxSupplyWei, formData.ownerAddress, \"\".concat(formData.tokenPrice, \" \").concat(formData.currency), formData.bonusTiers, \"\".concat(formData.tokenType, \" token deployed via admin panel\"), formData.tokenImageUrl || \"\", {\n                gasLimit\n            });\n        } else {\n            // For other networks, try to estimate gas\n            let gasLimit;\n            try {\n                // Estimate gas for deployToken function\n                const gasEstimate = await factory.deployToken.estimateGas(formData.name, formData.symbol, formData.decimals, maxSupplyWei, formData.ownerAddress, \"\".concat(formData.tokenPrice, \" \").concat(formData.currency), formData.bonusTiers, \"\".concat(formData.tokenType, \" token deployed via admin panel\"), formData.tokenImageUrl || \"\");\n                console.log(\"Gas estimate:\", gasEstimate.toString());\n                // Add 50% to the gas estimate to be safer\n                gasLimit = gasEstimate * BigInt(150) / BigInt(100);\n                console.log(\"Using calculated gas limit:\", gasLimit.toString());\n            } catch (estimateErr) {\n                console.error(\"Gas estimation failed, using fixed limit:\", estimateErr);\n                // Fallback to fixed gas limit if estimation fails\n                gasLimit = BigInt(2000000); // Use 2M for non-Amoy networks\n                console.log(\"Using fallback gas limit:\", gasLimit.toString());\n            }\n            // Call deployToken function\n            return await factory.deployToken(formData.name, formData.symbol, formData.decimals, maxSupplyWei, formData.ownerAddress, \"\".concat(formData.tokenPrice, \" \").concat(formData.currency), formData.bonusTiers, \"\".concat(formData.tokenType, \" token deployed via admin panel\"), formData.tokenImageUrl || \"\", {\n                gasLimit\n            });\n        }\n    };\n    /**\r\n   * Get deployment result after successful transaction\r\n   */ const getDeploymentResult = async (tokenAddress, provider, formData)=>{\n        console.log(\"Token successfully deployed at:\", tokenAddress);\n        // Connect to the token contract\n        const token = new ethers__WEBPACK_IMPORTED_MODULE_6__.Contract(tokenAddress, _contracts_SecurityToken_json__WEBPACK_IMPORTED_MODULE_3__.abi, provider);\n        // Get identity registry address (whitelist contract)\n        const whitelistAddress = await token.identityRegistry();\n        // Get token decimals from the contract\n        const tokenDecimals = await token.decimals();\n        const decimalsNumber = Number(tokenDecimals);\n        // Format maxSupply based on decimals\n        const maxSupplyRaw = await token.maxSupply();\n        const maxSupplyFormatted = decimalsNumber === 0 ? maxSupplyRaw.toString() : ethers__WEBPACK_IMPORTED_MODULE_7__.formatUnits(maxSupplyRaw, tokenDecimals);\n        // Try to get the image URL from the contract if supported\n        let tokenImageUrl = formData.tokenImageUrl;\n        try {\n            // Check if the token contract supports tokenImageUrl function\n            if (token.interface.fragments.some((fragment)=>fragment.type === \"function\" && 'name' in fragment && fragment.name === \"tokenImageUrl\")) {\n                tokenImageUrl = await token.tokenImageUrl();\n            }\n        } catch (error) {\n            console.log(\"Token contract doesn't support image URL, using form data\");\n        }\n        // Create deployed token object\n        return {\n            address: tokenAddress,\n            name: await token.name(),\n            symbol: await token.symbol(),\n            decimals: decimalsNumber,\n            maxSupply: maxSupplyFormatted,\n            whitelistAddress: whitelistAddress,\n            admin: formData.ownerAddress,\n            tokenPrice: \"\".concat(formData.tokenPrice, \" \").concat(formData.currency),\n            currency: formData.currency,\n            bonusTiers: formData.bonusTiers,\n            hasKYC: formData.enableKYC,\n            tokenImageUrl: tokenImageUrl\n        };\n    };\n    /**\r\n   * Handle deployment errors with detailed messages\r\n   */ const handleDeploymentError = (err, network)=>{\n        console.error('Error creating token:', err);\n        // Extract more details from the error for debugging\n        const errorDetails = typeof err === 'object' ? JSON.stringify({\n            code: err.code,\n            message: err.message,\n            data: err.data,\n            info: err.info\n        }, null, 2) : String(err);\n        // Special handling for specific contract errors\n        if (err.message.includes(\"transaction execution reverted\")) {\n            // This is likely a contract validation error\n            setError(\"Transaction failed: The contract rejected the transaction. This could be due to:\\n\\n• Token symbol already exists - try a different symbol\\n• Invalid parameters (empty name/symbol, zero max supply, etc.)\\n• Access control issues\\n\\nPlease check your inputs and try again with a unique token symbol.\\n\\nTechnical details: \".concat(err.message));\n        } else if (err.message.includes(\"gas required exceeds allowance\") || err.message.includes(\"intrinsic gas too low\") || err.message.includes(\"Internal JSON-RPC error\")) {\n            // For Amoy testnet specifically, provide CLI alternative\n            if (network === 'amoy') {\n                // Create a CLI command template - actual values will be filled in by the UI\n                const cliCommand = '# For Windows PowerShell:\\ncd Token\\n$env:NETWORK=\"amoy\"\\n$env:TOKEN_NAME=\"YourTokenName\"\\n$env:TOKEN_SYMBOL=\"YTS\"\\n$env:TOKEN_DECIMALS=\"0\"\\n$env:MAX_SUPPLY=\"1000000\"\\n$env:ADMIN_ADDRESS=\"0xYourAddress\"\\n$env:TOKEN_PRICE=\"10 USD\"\\n$env:BONUS_TIERS=\"Tier 1: 5%, Tier 2: 10%\"\\nnpx hardhat run scripts/02-deploy-token.js --network amoy';\n                setError(\"Gas estimation failed on Amoy testnet. This is a common issue with this network.\\n\\nYou can try using this command line script instead:\\n\\n\".concat(cliCommand));\n            } else {\n                setError(\"Transaction failed due to gas calculation issues: \".concat(err.message, \"\\n\\nDetails: \").concat(errorDetails));\n            }\n        } else if (err.message.includes(\"Internal JSON-RPC error\") || err.message.includes(\"could not coalesce error\")) {\n            setError(\"Transaction failed. This is likely due to gas calculation issues on the Amoy testnet. Try using the command line script instead.\");\n        } else {\n            setError(\"Transaction failed: \".concat(err.message, \"\\n\\nDetails: \").concat(errorDetails));\n        }\n        setDeploymentStep('failed');\n    };\n    return {\n        isSubmitting,\n        error,\n        success,\n        deployedToken,\n        transactionHash,\n        deploymentStep,\n        deployToken\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/create-token/hooks/useTokenDeployment.ts\n"));

/***/ })

});