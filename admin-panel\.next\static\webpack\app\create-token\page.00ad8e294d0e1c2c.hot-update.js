"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/create-token/page",{

/***/ "(app-pages-browser)/./src/app/create-token/hooks/useTokenDeployment.ts":
/*!**********************************************************!*\
  !*** ./src/app/create-token/hooks/useTokenDeployment.ts ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useTokenDeployment: () => (/* binding */ useTokenDeployment)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/providers/provider-browser.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/contract/contract.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/utils/units.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/constants/addresses.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/address/checks.js\");\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../config */ \"(app-pages-browser)/./src/config.ts\");\n/* harmony import */ var _contracts_ModularTokenFactory_json__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../contracts/ModularTokenFactory.json */ \"(app-pages-browser)/./src/contracts/ModularTokenFactory.json\");\n/* harmony import */ var _contracts_SecurityToken_json__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../contracts/SecurityToken.json */ \"(app-pages-browser)/./src/contracts/SecurityToken.json\");\n/* harmony import */ var _useERC3643Integration__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./useERC3643Integration */ \"(app-pages-browser)/./src/app/create-token/hooks/useERC3643Integration.ts\");\n\n\n\n\n\n\n/**\r\n * Custom hook for token deployment logic\r\n *\r\n * Encapsulates all the token deployment functionality including state management,\r\n * transaction handling, and error handling\r\n */ function useTokenDeployment(network, factoryAddress, hasDeployerRole, kycSupported) {\n    // State management\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [deployedToken, setDeployedToken] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [transactionHash, setTransactionHash] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [deploymentStep, setDeploymentStep] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('idle');\n    // ERC-3643 integration\n    const { setupERC3643Compliance, isERC3643Available } = (0,_useERC3643Integration__WEBPACK_IMPORTED_MODULE_4__.useERC3643Integration)();\n    /**\r\n   * Save token data to database\r\n   */ const saveTokenToDatabase = async (deployedToken, formData, transactionHash, blockNumber, network)=>{\n        // Fetch totalSupply from the blockchain\n        let totalSupply = '0';\n        try {\n            // Create a new provider instance for blockchain calls\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_5__.BrowserProvider(window.ethereum);\n            const token = new ethers__WEBPACK_IMPORTED_MODULE_6__.Contract(deployedToken.address, _contracts_SecurityToken_json__WEBPACK_IMPORTED_MODULE_3__.abi, provider);\n            const totalSupplyRaw = await token.totalSupply();\n            totalSupply = deployedToken.decimals === 0 ? totalSupplyRaw.toString() : ethers__WEBPACK_IMPORTED_MODULE_7__.formatUnits(totalSupplyRaw, deployedToken.decimals);\n        } catch (error) {\n            console.warn('Could not fetch totalSupply from blockchain, using default 0:', error);\n        }\n        const tokenData = {\n            address: deployedToken.address,\n            transactionHash: transactionHash,\n            blockNumber: blockNumber,\n            network: network,\n            name: deployedToken.name,\n            symbol: deployedToken.symbol,\n            decimals: deployedToken.decimals,\n            maxSupply: deployedToken.maxSupply,\n            totalSupply: totalSupply,\n            tokenType: formData.tokenType,\n            tokenPrice: deployedToken.tokenPrice,\n            currency: deployedToken.currency,\n            bonusTiers: deployedToken.bonusTiers,\n            tokenImageUrl: deployedToken.tokenImageUrl,\n            whitelistAddress: deployedToken.whitelistAddress,\n            adminAddress: deployedToken.admin,\n            hasKYC: deployedToken.hasKYC,\n            selectedClaims: formData.selectedClaims,\n            isActive: true,\n            deployedBy: deployedToken.admin,\n            deploymentNotes: \"\".concat(formData.tokenType, \" token deployed via admin panel\")\n        };\n        const response = await fetch('/api/tokens', {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify(tokenData)\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(\"Database save failed: \".concat(errorData.error || 'Unknown error'));\n        }\n        return await response.json();\n    };\n    /**\r\n   * Deploy a new token with the provided form data\r\n   */ const deployToken = async (formData)=>{\n        setIsSubmitting(true);\n        setError(null);\n        setSuccess(null);\n        setDeployedToken(null);\n        setTransactionHash(null);\n        setDeploymentStep('preparing');\n        try {\n            // Validate form data\n            validateFormData(formData);\n            // Get network configuration\n            const networkConfig = (0,_config__WEBPACK_IMPORTED_MODULE_1__.getNetworkConfig)(network);\n            if (!factoryAddress) {\n                throw new Error(\"No factory address configured for network: \".concat(network));\n            }\n            if (!window.ethereum) {\n                throw new Error('Please install MetaMask to use this feature!');\n            }\n            setDeploymentStep('connecting');\n            // Get provider and signer\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_5__.BrowserProvider(window.ethereum);\n            const signer = await provider.getSigner();\n            // Verify network connection\n            await verifyNetworkConnection(provider, network);\n            // Connect to the factory contract\n            const factory = new ethers__WEBPACK_IMPORTED_MODULE_6__.Contract(factoryAddress, _contracts_ModularTokenFactory_json__WEBPACK_IMPORTED_MODULE_2__, signer);\n            console.log(\"Connected to factory at:\", factoryAddress);\n            // Verify deployer role\n            await verifyDeployerRole(factory, signer, hasDeployerRole);\n            // Verify KYC support\n            await verifyKYCSupport(factory, formData.enableKYC);\n            // Check if token symbol already exists\n            await checkTokenSymbolAvailability(factory, formData.symbol);\n            // Convert maxSupply to the appropriate unit based on decimals\n            const maxSupplyWei = formData.decimals === 0 ? BigInt(formData.maxSupply) : ethers__WEBPACK_IMPORTED_MODULE_7__.parseUnits(formData.maxSupply, formData.decimals);\n            setDeploymentStep('deploying');\n            console.log(\"Deploying token with params:\", {\n                name: formData.name,\n                symbol: formData.symbol,\n                decimals: formData.decimals,\n                maxSupply: formData.maxSupply,\n                admin: formData.ownerAddress,\n                tokenPrice: formData.tokenPrice,\n                bonusTiers: formData.bonusTiers,\n                enableKYC: formData.enableKYC\n            });\n            // Create the transaction\n            const tx = await createDeployTransaction(factory, formData, maxSupplyWei, network, kycSupported);\n            setTransactionHash(tx.hash);\n            console.log(\"Transaction hash:\", tx.hash);\n            setDeploymentStep('confirming');\n            // Wait for the transaction to be mined\n            const receipt = await tx.wait();\n            console.log(\"Transaction mined in block:\", receipt.blockNumber);\n            setDeploymentStep('fetching');\n            // Get the token address from the transaction receipt events\n            let tokenAddress;\n            const deployEvent = receipt.logs.find((log)=>{\n                try {\n                    const parsed = factory.interface.parseLog(log);\n                    return parsed && parsed.name === 'TokenDeployed';\n                } catch (e) {\n                    return false;\n                }\n            });\n            if (deployEvent) {\n                const parsed = factory.interface.parseLog(deployEvent);\n                if (parsed) {\n                    tokenAddress = parsed.args.tokenAddress;\n                }\n            }\n            if (!tokenAddress) {\n                // Fallback: get the latest deployed token\n                const deployedTokens = await factory.getDeployedTokens(0, 100);\n                tokenAddress = deployedTokens.tokens[deployedTokens.tokens.length - 1];\n            }\n            if (tokenAddress && tokenAddress !== ethers__WEBPACK_IMPORTED_MODULE_8__.ZeroAddress) {\n                // Create token deployment result\n                const deploymentResult = await getDeploymentResult(tokenAddress, provider, formData);\n                setDeployedToken(deploymentResult);\n                // Save token to database\n                try {\n                    await saveTokenToDatabase(deploymentResult, formData, tx.hash, receipt.blockNumber.toString(), network);\n                    console.log(\"Token successfully saved to database\");\n                } catch (dbError) {\n                    console.warn(\"Failed to save token to database:\", dbError);\n                // Don't fail the deployment if database save fails\n                }\n                // Setup ERC-3643 compliance if available\n                if (isERC3643Available()) {\n                    setDeploymentStep('setting_up_compliance');\n                    console.log(\"🏛️ Setting up ERC-3643 compliance...\");\n                    try {\n                        const complianceResult = await setupERC3643Compliance(tokenAddress, formData.ownerAddress, signer, {\n                            name: formData.name,\n                            symbol: formData.symbol,\n                            tokenType: formData.tokenType,\n                            country: formData.issuerCountry || 'US',\n                            selectedClaims: formData.selectedClaims\n                        });\n                        if (complianceResult.errors.length > 0) {\n                            console.warn(\"⚠️ Some ERC-3643 setup steps failed:\", complianceResult.errors);\n                        // Don't fail deployment, just warn\n                        } else {\n                            console.log(\"✅ ERC-3643 compliance setup completed successfully\");\n                        }\n                    } catch (complianceError) {\n                        console.warn(\"⚠️ ERC-3643 compliance setup failed:\", complianceError);\n                    // Don't fail deployment, just warn\n                    }\n                } else {\n                    console.log(\"ℹ️ ERC-3643 contracts not available, skipping compliance setup\");\n                }\n                setDeploymentStep('completed');\n                setSuccess('Token \"'.concat(formData.name, '\" (').concat(formData.symbol, \") successfully deployed!\"));\n            } else {\n                throw new Error(\"Token deployment failed: Could not retrieve token address\");\n            }\n        } catch (err) {\n            handleDeploymentError(err, network);\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    /**\r\n   * Validate form data before deployment\r\n   */ const validateFormData = (formData)=>{\n        if (!formData.name || !formData.symbol || !formData.maxSupply || !formData.ownerAddress) {\n            throw new Error('Please fill in all required fields');\n        }\n        if (!ethers__WEBPACK_IMPORTED_MODULE_9__.isAddress(formData.ownerAddress)) {\n            throw new Error('Invalid owner address');\n        }\n        if (formData.decimals < 0 || formData.decimals > 18) {\n            throw new Error('Decimals must be between 0 and 18');\n        }\n    };\n    /**\r\n   * Verify that the wallet is connected to the correct network\r\n   */ const verifyNetworkConnection = async (provider, network)=>{\n        const chainId = (await provider.getNetwork()).chainId;\n        if (network === 'amoy' && chainId.toString() !== '80002') {\n            throw new Error('Please connect your wallet to the Amoy network (Chain ID: 80002)');\n        } else if (network === 'polygon' && chainId.toString() !== '137') {\n            throw new Error('Please connect your wallet to the Polygon network (Chain ID: 137)');\n        }\n    };\n    /**\r\n   * Verify that the connected wallet has DEPLOYER_ROLE\r\n   */ const verifyDeployerRole = async (factory, signer, hasRole)=>{\n        if (!hasRole) {\n            const DEPLOYER_ROLE = await factory.DEPLOYER_ROLE();\n            const hasDeployerRole = await factory.hasRole(DEPLOYER_ROLE, await signer.getAddress());\n            if (!hasDeployerRole) {\n                throw new Error(\"Your wallet does not have the DEPLOYER_ROLE required to create tokens\");\n            }\n        }\n    };\n    /**\r\n   * Check if token symbol is available\r\n   */ const checkTokenSymbolAvailability = async (factory, symbol)=>{\n        try {\n            // ModularTokenFactory doesn't have getTokenAddressBySymbol\n            // We'll check by getting all deployed tokens and checking symbols\n            const deployedTokens = await factory.getDeployedTokens(0, 1000); // Get up to 1000 tokens\n            for (const tokenAddr of deployedTokens.tokens){\n                try {\n                    const token = new ethers__WEBPACK_IMPORTED_MODULE_6__.Contract(tokenAddr, [\n                        'function symbol() view returns (string)'\n                    ], factory.runner);\n                    const existingSymbol = await token.symbol();\n                    if (existingSymbol === symbol) {\n                        throw new Error('Token with symbol \"'.concat(symbol, '\" already exists at address ').concat(tokenAddr, \". Please choose a different symbol.\"));\n                    }\n                } catch (tokenErr) {\n                    // Skip tokens that can't be read (might be different contract types)\n                    if (!tokenErr.message.includes(\"already exists\")) {\n                        continue;\n                    }\n                    throw tokenErr;\n                }\n            }\n        } catch (err) {\n            if (err.message.includes(\"already exists\")) {\n                throw err; // Re-throw our custom error\n            }\n            // If it's a different error, log it but don't fail the deployment\n            console.warn(\"Could not check token symbol availability:\", err.message);\n        }\n    };\n    /**\r\n   * Verify KYC support in the factory contract\r\n   */ const verifyKYCSupport = async (factory, enableKYC)=>{\n        // ModularTokenFactory always supports basic deployment\n        // KYC functionality is handled through modules, not deployment options\n        console.log(\"ModularTokenFactory detected - KYC will be handled through modules\");\n    };\n    /**\r\n   * Create the deployment transaction with the appropriate gas settings\r\n   */ const createDeployTransaction = async (factory, formData, maxSupplyWei, network, supportsKYC)=>{\n        // ModularTokenFactory uses deployToken function\n        console.log(\"Using ModularTokenFactory.deployToken function\");\n        // Optimized gas settings for Amoy testnet\n        if (network === 'amoy') {\n            // Optimized gas settings for Amoy testnet - let the network determine gas price\n            const gasLimit = BigInt(1000000); // 1M gas should be enough\n            console.log(\"Using optimized gas settings for Amoy testnet:\");\n            console.log(\"Gas limit:\", gasLimit.toString());\n            console.log(\"Gas price: auto (network determined)\");\n            // Call ModularTokenFactory.deployToken\n            console.log(\"Calling ModularTokenFactory.deployToken\");\n            return await factory.deployToken(formData.name, formData.symbol, formData.decimals, maxSupplyWei, formData.ownerAddress, \"\".concat(formData.tokenPrice, \" \").concat(formData.currency), formData.bonusTiers, \"\".concat(formData.tokenType, \" token deployed via admin panel\"), formData.tokenImageUrl || \"\", {\n                gasLimit,\n                gasPrice\n            });\n        } else {\n            // For other networks, try to estimate gas\n            let gasLimit;\n            try {\n                // Estimate gas for deployToken function\n                const gasEstimate = await factory.deployToken.estimateGas(formData.name, formData.symbol, formData.decimals, maxSupplyWei, formData.ownerAddress, \"\".concat(formData.tokenPrice, \" \").concat(formData.currency), formData.bonusTiers, \"\".concat(formData.tokenType, \" token deployed via admin panel\"), formData.tokenImageUrl || \"\");\n                console.log(\"Gas estimate:\", gasEstimate.toString());\n                // Add 50% to the gas estimate to be safer\n                gasLimit = gasEstimate * BigInt(150) / BigInt(100);\n                console.log(\"Using calculated gas limit:\", gasLimit.toString());\n            } catch (estimateErr) {\n                console.error(\"Gas estimation failed, using fixed limit:\", estimateErr);\n                // Fallback to fixed gas limit if estimation fails\n                gasLimit = BigInt(2000000); // Use 2M for non-Amoy networks\n                console.log(\"Using fallback gas limit:\", gasLimit.toString());\n            }\n            // Call deployToken function\n            return await factory.deployToken(formData.name, formData.symbol, formData.decimals, maxSupplyWei, formData.ownerAddress, \"\".concat(formData.tokenPrice, \" \").concat(formData.currency), formData.bonusTiers, \"\".concat(formData.tokenType, \" token deployed via admin panel\"), formData.tokenImageUrl || \"\", {\n                gasLimit\n            });\n        }\n    };\n    /**\r\n   * Get deployment result after successful transaction\r\n   */ const getDeploymentResult = async (tokenAddress, provider, formData)=>{\n        console.log(\"Token successfully deployed at:\", tokenAddress);\n        // Connect to the token contract\n        const token = new ethers__WEBPACK_IMPORTED_MODULE_6__.Contract(tokenAddress, _contracts_SecurityToken_json__WEBPACK_IMPORTED_MODULE_3__.abi, provider);\n        // Get identity registry address (whitelist contract)\n        const whitelistAddress = await token.identityRegistry();\n        // Get token decimals from the contract\n        const tokenDecimals = await token.decimals();\n        const decimalsNumber = Number(tokenDecimals);\n        // Format maxSupply based on decimals\n        const maxSupplyRaw = await token.maxSupply();\n        const maxSupplyFormatted = decimalsNumber === 0 ? maxSupplyRaw.toString() : ethers__WEBPACK_IMPORTED_MODULE_7__.formatUnits(maxSupplyRaw, tokenDecimals);\n        // Try to get the image URL from the contract if supported\n        let tokenImageUrl = formData.tokenImageUrl;\n        try {\n            // Check if the token contract supports tokenImageUrl function\n            if (token.interface.fragments.some((fragment)=>fragment.type === \"function\" && 'name' in fragment && fragment.name === \"tokenImageUrl\")) {\n                tokenImageUrl = await token.tokenImageUrl();\n            }\n        } catch (error) {\n            console.log(\"Token contract doesn't support image URL, using form data\");\n        }\n        // Create deployed token object\n        return {\n            address: tokenAddress,\n            name: await token.name(),\n            symbol: await token.symbol(),\n            decimals: decimalsNumber,\n            maxSupply: maxSupplyFormatted,\n            whitelistAddress: whitelistAddress,\n            admin: formData.ownerAddress,\n            tokenPrice: \"\".concat(formData.tokenPrice, \" \").concat(formData.currency),\n            currency: formData.currency,\n            bonusTiers: formData.bonusTiers,\n            hasKYC: formData.enableKYC,\n            tokenImageUrl: tokenImageUrl\n        };\n    };\n    /**\r\n   * Handle deployment errors with detailed messages\r\n   */ const handleDeploymentError = (err, network)=>{\n        console.error('Error creating token:', err);\n        // Extract more details from the error for debugging\n        const errorDetails = typeof err === 'object' ? JSON.stringify({\n            code: err.code,\n            message: err.message,\n            data: err.data,\n            info: err.info\n        }, null, 2) : String(err);\n        // Special handling for specific contract errors\n        if (err.message.includes(\"transaction execution reverted\")) {\n            // This is likely a contract validation error\n            setError(\"Transaction failed: The contract rejected the transaction. This could be due to:\\n\\n• Token symbol already exists - try a different symbol\\n• Invalid parameters (empty name/symbol, zero max supply, etc.)\\n• Access control issues\\n\\nPlease check your inputs and try again with a unique token symbol.\\n\\nTechnical details: \".concat(err.message));\n        } else if (err.message.includes(\"gas required exceeds allowance\") || err.message.includes(\"intrinsic gas too low\") || err.message.includes(\"Internal JSON-RPC error\")) {\n            // For Amoy testnet specifically, provide CLI alternative\n            if (network === 'amoy') {\n                // Create a CLI command template - actual values will be filled in by the UI\n                const cliCommand = '# For Windows PowerShell:\\ncd Token\\n$env:NETWORK=\"amoy\"\\n$env:TOKEN_NAME=\"YourTokenName\"\\n$env:TOKEN_SYMBOL=\"YTS\"\\n$env:TOKEN_DECIMALS=\"0\"\\n$env:MAX_SUPPLY=\"1000000\"\\n$env:ADMIN_ADDRESS=\"0xYourAddress\"\\n$env:TOKEN_PRICE=\"10 USD\"\\n$env:BONUS_TIERS=\"Tier 1: 5%, Tier 2: 10%\"\\nnpx hardhat run scripts/02-deploy-token.js --network amoy';\n                setError(\"Gas estimation failed on Amoy testnet. This is a common issue with this network.\\n\\nYou can try using this command line script instead:\\n\\n\".concat(cliCommand));\n            } else {\n                setError(\"Transaction failed due to gas calculation issues: \".concat(err.message, \"\\n\\nDetails: \").concat(errorDetails));\n            }\n        } else if (err.message.includes(\"Internal JSON-RPC error\") || err.message.includes(\"could not coalesce error\")) {\n            setError(\"Transaction failed. This is likely due to gas calculation issues on the Amoy testnet. Try using the command line script instead.\");\n        } else {\n            setError(\"Transaction failed: \".concat(err.message, \"\\n\\nDetails: \").concat(errorDetails));\n        }\n        setDeploymentStep('failed');\n    };\n    return {\n        isSubmitting,\n        error,\n        success,\n        deployedToken,\n        transactionHash,\n        deploymentStep,\n        deployToken\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/create-token/hooks/useTokenDeployment.ts\n"));

/***/ })

});