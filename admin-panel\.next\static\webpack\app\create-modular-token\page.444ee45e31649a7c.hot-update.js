"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/create-modular-token/page",{

/***/ "(app-pages-browser)/./src/app/create-modular-token/page.tsx":
/*!***********************************************!*\
  !*** ./src/app/create-modular-token/page.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CreateModularTokenPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/providers/provider-browser.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/utils/units.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/contract/contract.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n// Contract addresses from environment\nconst SECURITY_TOKEN_CORE_ADDRESS = \"******************************************\";\nconst UPGRADE_MANAGER_ADDRESS = \"******************************************\";\nconst MODULAR_TOKEN_FACTORY_ADDRESS = \"******************************************\";\nfunction CreateModularTokenPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    // State Management\n    const [provider, setProvider] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [signer, setSigner] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [deployedToken, setDeployedToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [hasDeployerRole, setHasDeployerRole] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isCheckingRole, setIsCheckingRole] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        symbol: '',\n        decimals: 0,\n        maxSupply: '10000000',\n        adminAddress: '',\n        tokenPrice: '1.00',\n        currency: 'USD',\n        bonusTiers: 'Early Bird: 10%, Standard: 5%, Late: 0%',\n        tokenDetails: 'Security token with advanced compliance features',\n        tokenImageUrl: ''\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CreateModularTokenPage.useEffect\": ()=>{\n            initializeProvider();\n        }\n    }[\"CreateModularTokenPage.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CreateModularTokenPage.useEffect\": ()=>{\n            if (signer && MODULAR_TOKEN_FACTORY_ADDRESS) {\n                checkDeployerRole();\n            }\n        }\n    }[\"CreateModularTokenPage.useEffect\"], [\n        signer,\n        MODULAR_TOKEN_FACTORY_ADDRESS\n    ]);\n    const initializeProvider = async ()=>{\n        try {\n            if ( true && window.ethereum) {\n                const provider = new ethers__WEBPACK_IMPORTED_MODULE_4__.BrowserProvider(window.ethereum);\n                await provider.send('eth_requestAccounts', []);\n                // Check network\n                const network = await provider.getNetwork();\n                console.log('Connected to network:', network.name, 'Chain ID:', network.chainId.toString());\n                // Amoy testnet chain ID is 80002\n                if (network.chainId !== 80002n) {\n                    setError(\"Wrong network! Please switch to Amoy testnet (Chain ID: 80002). Currently on: \".concat(network.chainId.toString()));\n                    return;\n                }\n                const signer = await provider.getSigner();\n                const address = await signer.getAddress();\n                // Check balance\n                const balance = await provider.getBalance(address);\n                console.log('Wallet balance:', ethers__WEBPACK_IMPORTED_MODULE_5__.formatEther(balance), 'ETH');\n                if (balance < ethers__WEBPACK_IMPORTED_MODULE_5__.parseEther('0.01')) {\n                    setError('Insufficient balance. You need at least 0.01 ETH for gas fees.');\n                    return;\n                }\n                setProvider(provider);\n                setSigner(signer);\n                setFormData((prev)=>({\n                        ...prev,\n                        adminAddress: address\n                    }));\n            } else {\n                setError('MetaMask not found. Please install MetaMask to create tokens.');\n            }\n        } catch (error) {\n            console.error('Error initializing provider:', error);\n            setError('Failed to connect to wallet');\n        }\n    };\n    const checkDeployerRole = async ()=>{\n        if (!signer || !MODULAR_TOKEN_FACTORY_ADDRESS) return;\n        try {\n            const ModularTokenFactoryABI = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_contracts_ModularTokenFactory_json\").then(__webpack_require__.t.bind(__webpack_require__, /*! ../../contracts/ModularTokenFactory.json */ \"(app-pages-browser)/./src/contracts/ModularTokenFactory.json\", 19));\n            const factory = new ethers__WEBPACK_IMPORTED_MODULE_6__.Contract(MODULAR_TOKEN_FACTORY_ADDRESS, ModularTokenFactoryABI.default, signer);\n            const DEPLOYER_ROLE = await factory.DEPLOYER_ROLE();\n            const userAddress = await signer.getAddress();\n            const hasRole = await factory.hasRole(DEPLOYER_ROLE, userAddress);\n            setHasDeployerRole(hasRole);\n        } catch (error) {\n            console.error('Error checking deployer role:', error);\n            setHasDeployerRole(false);\n        }\n    };\n    const requestDeployerRole = async ()=>{\n        if (!signer || !MODULAR_TOKEN_FACTORY_ADDRESS) return;\n        setIsCheckingRole(true);\n        setError(null);\n        try {\n            const ModularTokenFactoryABI = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_contracts_ModularTokenFactory_json\").then(__webpack_require__.t.bind(__webpack_require__, /*! ../../contracts/ModularTokenFactory.json */ \"(app-pages-browser)/./src/contracts/ModularTokenFactory.json\", 19));\n            const factory = new ethers__WEBPACK_IMPORTED_MODULE_6__.Contract(MODULAR_TOKEN_FACTORY_ADDRESS, ModularTokenFactoryABI.default, signer);\n            const DEPLOYER_ROLE = await factory.DEPLOYER_ROLE();\n            const userAddress = await signer.getAddress();\n            // Try to grant the role (this will only work if the user has admin role)\n            const tx = await factory.grantRole(DEPLOYER_ROLE, userAddress);\n            await tx.wait();\n            setHasDeployerRole(true);\n            setSuccess('Deployer role granted successfully!');\n        } catch (error) {\n            console.error('Error granting deployer role:', error);\n            if (error.message.includes('AccessControlUnauthorizedAccount')) {\n                setError('You do not have permission to grant roles. Please contact an administrator to grant you the DEPLOYER_ROLE.');\n            } else {\n                setError(\"Failed to grant deployer role: \".concat(error.message));\n            }\n        } finally{\n            setIsCheckingRole(false);\n        }\n    };\n    const handleInputChange = (e)=>{\n        const { name, value, type } = e.target;\n        let processedValue = value;\n        if (name === 'decimals') {\n            processedValue = parseInt(value, 10);\n        }\n        setFormData({\n            ...formData,\n            [name]: processedValue\n        });\n    };\n    const deployModularToken = async ()=>{\n        if (!signer || !MODULAR_TOKEN_FACTORY_ADDRESS) {\n            setError('Please connect your wallet first');\n            return;\n        }\n        setIsSubmitting(true);\n        setError(null);\n        setSuccess(null);\n        try {\n            // Import the ModularTokenFactory ABI\n            const ModularTokenFactoryABI = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_contracts_ModularTokenFactory_json\").then(__webpack_require__.t.bind(__webpack_require__, /*! ../../contracts/ModularTokenFactory.json */ \"(app-pages-browser)/./src/contracts/ModularTokenFactory.json\", 19));\n            // Create factory contract instance\n            const factory = new ethers__WEBPACK_IMPORTED_MODULE_6__.Contract(MODULAR_TOKEN_FACTORY_ADDRESS, ModularTokenFactoryABI.default, signer);\n            // Check if user has DEPLOYER_ROLE\n            if (hasDeployerRole === false) {\n                setError('You do not have permission to deploy tokens. Please contact an administrator to grant you the DEPLOYER_ROLE.');\n                return;\n            }\n            // Double-check role if we haven't checked yet\n            if (hasDeployerRole === null) {\n                const DEPLOYER_ROLE = await factory.DEPLOYER_ROLE();\n                const userAddress = await signer.getAddress();\n                const roleCheck = await factory.hasRole(DEPLOYER_ROLE, userAddress);\n                if (!roleCheck) {\n                    setHasDeployerRole(false);\n                    setError('You do not have permission to deploy tokens. Please contact an administrator to grant you the DEPLOYER_ROLE.');\n                    return;\n                }\n                setHasDeployerRole(true);\n            }\n            // Convert max supply to proper units\n            const maxSupplyWei = ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits(formData.maxSupply, formData.decimals);\n            // Combine price and currency\n            const tokenPriceWithCurrency = \"\".concat(formData.tokenPrice, \" \").concat(formData.currency);\n            // Deploy the token through the factory\n            console.log('Deploying token with params:', {\n                name: formData.name,\n                symbol: formData.symbol,\n                decimals: formData.decimals,\n                maxSupply: formData.maxSupply,\n                admin: formData.adminAddress,\n                tokenPrice: tokenPriceWithCurrency,\n                bonusTiers: formData.bonusTiers,\n                tokenDetails: formData.tokenDetails,\n                tokenImageUrl: formData.tokenImageUrl\n            });\n            // First try to estimate gas\n            let gasEstimate;\n            let useFixedGas = false;\n            try {\n                gasEstimate = await factory.deployToken.estimateGas(formData.name, formData.symbol, formData.decimals, maxSupplyWei, formData.adminAddress, tokenPriceWithCurrency, formData.bonusTiers, formData.tokenDetails, formData.tokenImageUrl);\n                console.log('Gas estimate:', gasEstimate.toString());\n            } catch (gasError) {\n                console.warn('Gas estimation failed, using fixed gas limit:', gasError.message);\n                gasEstimate = BigInt(1000000); // 1M gas as conservative fallback\n                useFixedGas = true;\n            }\n            // Add 50% buffer to gas estimate for safety\n            const gasLimit = useFixedGas ? gasEstimate : gasEstimate + gasEstimate * BigInt(50) / BigInt(100);\n            console.log('Using gas limit:', gasLimit.toString());\n            // Try deployment with minimal transaction options to let MetaMask handle gas\n            let tx;\n            try {\n                // First attempt: Let MetaMask estimate gas\n                tx = await factory.deployToken(formData.name, formData.symbol, formData.decimals, maxSupplyWei, formData.adminAddress, tokenPriceWithCurrency, formData.bonusTiers, formData.tokenDetails, formData.tokenImageUrl);\n            } catch (firstError) {\n                console.warn('First attempt failed, trying with manual gas settings:', firstError.message);\n                // Second attempt: Use manual gas settings\n                const feeData = await signer.provider.getFeeData();\n                const txOptions = {\n                    gasLimit: gasLimit,\n                    maxFeePerGas: feeData.maxFeePerGas || ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits('60', 'gwei'),\n                    maxPriorityFeePerGas: feeData.maxPriorityFeePerGas || ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits('2', 'gwei')\n                };\n                console.log('Retry with transaction options:', {\n                    gasLimit: txOptions.gasLimit.toString(),\n                    maxFeePerGas: ethers__WEBPACK_IMPORTED_MODULE_5__.formatUnits(txOptions.maxFeePerGas, 'gwei') + ' gwei',\n                    maxPriorityFeePerGas: ethers__WEBPACK_IMPORTED_MODULE_5__.formatUnits(txOptions.maxPriorityFeePerGas, 'gwei') + ' gwei'\n                });\n                tx = await factory.deployToken(formData.name, formData.symbol, formData.decimals, maxSupplyWei, formData.adminAddress, tokenPriceWithCurrency, formData.bonusTiers, formData.tokenDetails, formData.tokenImageUrl, txOptions);\n            }\n            console.log('Transaction sent:', tx.hash);\n            setSuccess('Transaction sent! Waiting for confirmation...');\n            const receipt = await tx.wait();\n            console.log('Transaction confirmed:', receipt);\n            // Get the deployed token address from the event\n            const event = receipt.logs.find((log)=>{\n                try {\n                    const parsed = factory.interface.parseLog(log);\n                    return parsed.name === 'TokenDeployed';\n                } catch (e) {\n                    return false;\n                }\n            });\n            if (event) {\n                const parsedEvent = factory.interface.parseLog(event);\n                const tokenAddress = parsedEvent.args.tokenAddress;\n                setDeployedToken({\n                    address: tokenAddress,\n                    name: formData.name,\n                    symbol: formData.symbol,\n                    transactionHash: tx.hash\n                });\n                setSuccess(\"Modular token deployed successfully! Address: \".concat(tokenAddress));\n            } else {\n                setError('Token deployed but could not find deployment event. Please check the transaction.');\n            }\n        } catch (error) {\n            var _error_error_message, _error_error;\n            console.error('Error deploying modular token:', error);\n            // Parse common error messages\n            let errorMessage = error.message;\n            if (error.code === 'UNKNOWN_ERROR' && ((_error_error = error.error) === null || _error_error === void 0 ? void 0 : (_error_error_message = _error_error.message) === null || _error_error_message === void 0 ? void 0 : _error_error_message.includes('Internal JSON-RPC error'))) {\n                errorMessage = 'Network error: The Amoy testnet is experiencing issues. Please try again in a few minutes.';\n            } else if (error.message.includes('AccessControlUnauthorizedAccount')) {\n                errorMessage = 'You do not have permission to deploy tokens. Please contact an administrator.';\n            } else if (error.message.includes('execution reverted')) {\n                errorMessage = 'Transaction failed. Please check your inputs and try again.';\n            } else if (error.message.includes('gas')) {\n                errorMessage = 'Gas estimation failed. The network may be congested. Please try again.';\n            } else if (error.message.includes('fee') && error.message.includes('cap')) {\n                errorMessage = 'Transaction fee too high. Please try again when network fees are lower.';\n            } else if (error.code === 'NETWORK_ERROR') {\n                errorMessage = 'Network connection error. Please check your internet connection and try again.';\n            } else if (error.code === 'TIMEOUT') {\n                errorMessage = 'Transaction timeout. The network may be slow. Please try again.';\n            }\n            setError(\"Failed to deploy modular token: \".concat(errorMessage));\n            // Log additional debugging info\n            console.log('Error details:', {\n                code: error.code,\n                reason: error.reason,\n                data: error.data,\n                transaction: error.transaction\n            });\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        // Validation\n        if (!formData.name || !formData.symbol || !formData.adminAddress) {\n            setError('Please fill in all required fields');\n            return;\n        }\n        if (formData.decimals < 0 || formData.decimals > 18) {\n            setError('Decimals must be between 0 and 18');\n            return;\n        }\n        await deployModularToken();\n    };\n    if (!provider || !signer) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6 flex items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/\",\n                            className: \"text-blue-600 hover:text-blue-800 mr-4\",\n                            children: \"← Back to Dashboard\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                            lineNumber: 404,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold\",\n                            children: \"Create Modular Security Token\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                            lineNumber: 407,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                    lineNumber: 403,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-bold mb-2\",\n                            children: \"Connect Wallet\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                            lineNumber: 411,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-4\",\n                            children: \"Please connect your MetaMask wallet to create modular tokens.\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                            lineNumber: 412,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: initializeProvider,\n                            className: \"bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded\",\n                            children: \"Connect Wallet\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                            lineNumber: 415,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                    lineNumber: 410,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n            lineNumber: 402,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 flex items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/\",\n                        className: \"text-blue-600 hover:text-blue-800 mr-4\",\n                        children: \"← Back to Dashboard\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                        lineNumber: 429,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold\",\n                        children: \"Create Modular Security Token\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                        lineNumber: 432,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"ml-4 px-3 py-1 text-xs font-medium rounded-full bg-purple-100 text-purple-800\",\n                        children: \"Amoy Testnet\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                        lineNumber: 433,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                lineNumber: 428,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 p-4 bg-purple-50 border border-purple-200 rounded-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-shrink-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-purple-600 text-xl\",\n                                children: \"\\uD83D\\uDE80\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                lineNumber: 442,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                            lineNumber: 441,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"ml-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-sm font-medium text-purple-800\",\n                                    children: \"Next-Generation Modular Architecture\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                    lineNumber: 445,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-1 text-sm text-purple-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"Create tokens using our advanced modular architecture with:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                            lineNumber: 449,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"mt-1 list-disc list-inside space-y-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Upgradeable Contracts:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                            lineNumber: 451,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" Future-proof with secure upgrade mechanisms\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                    lineNumber: 451,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Modular Design:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                            lineNumber: 452,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" Add new features without redeployment\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                    lineNumber: 452,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Enhanced Security:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                            lineNumber: 453,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" Timelock protection and emergency controls\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                    lineNumber: 453,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Gas Optimization:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                            lineNumber: 454,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" Efficient proxy pattern implementation\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                    lineNumber: 454,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                            lineNumber: 450,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                    lineNumber: 448,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                            lineNumber: 444,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                    lineNumber: 440,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                lineNumber: 439,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-sm font-medium text-blue-800 mb-2\",\n                        children: \"Modular Architecture Contracts\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                        lineNumber: 463,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-blue-700 space-y-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"ModularTokenFactory:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 466,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-2 font-mono text-xs\",\n                                        children: MODULAR_TOKEN_FACTORY_ADDRESS\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 467,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                lineNumber: 465,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"SecurityTokenCore Implementation:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 470,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-2 font-mono text-xs\",\n                                        children: SECURITY_TOKEN_CORE_ADDRESS\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 471,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                lineNumber: 469,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"UpgradeManager:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 474,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-2 font-mono text-xs\",\n                                        children: UPGRADE_MANAGER_ADDRESS\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 475,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                lineNumber: 473,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                        lineNumber: 464,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                lineNumber: 462,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 p-4 bg-gray-50 border border-gray-200 rounded-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-sm font-medium text-gray-800 mb-2\",\n                        children: \"Deployment Permissions\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                        lineNumber: 482,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-700\",\n                                        children: \"DEPLOYER_ROLE Status:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 485,\n                                        columnNumber: 13\n                                    }, this),\n                                    hasDeployerRole === null ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-yellow-600\",\n                                        children: \"⏳ Checking...\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 487,\n                                        columnNumber: 15\n                                    }, this) : hasDeployerRole ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-green-600\",\n                                        children: \"✅ Granted\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 489,\n                                        columnNumber: 15\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-red-600\",\n                                        children: \"❌ Not Granted\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 491,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                lineNumber: 484,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: checkDeployerRole,\n                                        disabled: isCheckingRole,\n                                        className: \"bg-gray-600 hover:bg-gray-700 text-white font-medium py-1 px-3 rounded text-sm disabled:opacity-50\",\n                                        children: \"\\uD83D\\uDD04 Refresh\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 495,\n                                        columnNumber: 13\n                                    }, this),\n                                    hasDeployerRole === false && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: requestDeployerRole,\n                                        disabled: isCheckingRole,\n                                        className: \"bg-blue-600 hover:bg-blue-700 text-white font-medium py-1 px-3 rounded text-sm disabled:opacity-50\",\n                                        children: isCheckingRole ? '⏳ Requesting...' : '🔑 Request Role'\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 503,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                lineNumber: 494,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                        lineNumber: 483,\n                        columnNumber: 9\n                    }, this),\n                    hasDeployerRole === false && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs text-gray-600 mt-2\",\n                        children: \"You need the DEPLOYER_ROLE to create tokens. Contact an administrator if the request fails.\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                        lineNumber: 514,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                lineNumber: 481,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-red-600 mr-2\",\n                            children: \"⚠️\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                            lineNumber: 524,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                            lineNumber: 525,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                    lineNumber: 523,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                lineNumber: 522,\n                columnNumber: 9\n            }, this),\n            success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-green-600 mr-2\",\n                            children: \"✅\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                            lineNumber: 534,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: success\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                            lineNumber: 535,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                    lineNumber: 533,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                lineNumber: 532,\n                columnNumber: 9\n            }, this),\n            deployedToken && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 bg-green-50 border border-green-200 rounded-lg p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-green-800 mb-4\",\n                        children: \"\\uD83C\\uDF89 Modular Token Created Successfully!\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                        lineNumber: 543,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2 text-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"Token Name:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 546,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: deployedToken.name\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 547,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                lineNumber: 545,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"Symbol:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 550,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: deployedToken.symbol\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 551,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                lineNumber: 549,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"Contract Address:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 554,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-mono text-xs\",\n                                        children: deployedToken.address\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 555,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                lineNumber: 553,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                        lineNumber: 544,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 flex gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>window.open(\"https://amoy.polygonscan.com/address/\".concat(deployedToken.address), '_blank'),\n                                className: \"bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded text-sm\",\n                                children: \"View on PolygonScan\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                lineNumber: 559,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/modular-tokens\",\n                                className: \"bg-purple-600 hover:bg-purple-700 text-white font-medium py-2 px-4 rounded text-sm\",\n                                children: \"Manage Token\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                lineNumber: 565,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                        lineNumber: 558,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                lineNumber: 542,\n                columnNumber: 9\n            }, this),\n            !deployedToken && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-bold mb-4\",\n                        children: \"Token Configuration\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                        lineNumber: 578,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit,\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"name\",\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Token Name *\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                lineNumber: 583,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                id: \"name\",\n                                                name: \"name\",\n                                                value: formData.name,\n                                                onChange: handleInputChange,\n                                                placeholder: \"e.g., Augment Security Token\",\n                                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                lineNumber: 586,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 582,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"symbol\",\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Token Symbol *\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                lineNumber: 599,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                id: \"symbol\",\n                                                name: \"symbol\",\n                                                value: formData.symbol,\n                                                onChange: handleInputChange,\n                                                placeholder: \"e.g., AST\",\n                                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                lineNumber: 602,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 598,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"decimals\",\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Decimals *\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                lineNumber: 615,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                id: \"decimals\",\n                                                name: \"decimals\",\n                                                value: formData.decimals,\n                                                onChange: handleInputChange,\n                                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                                required: true,\n                                                children: [\n                                                    ...Array(19)\n                                                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: i,\n                                                        children: [\n                                                            i,\n                                                            \" decimals\"\n                                                        ]\n                                                    }, i, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                        lineNumber: 627,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                lineNumber: 618,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 614,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"maxSupply\",\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Maximum Supply *\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                lineNumber: 633,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"number\",\n                                                id: \"maxSupply\",\n                                                name: \"maxSupply\",\n                                                value: formData.maxSupply,\n                                                onChange: handleInputChange,\n                                                placeholder: \"10000000\",\n                                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                lineNumber: 636,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 632,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                lineNumber: 581,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"adminAddress\",\n                                        className: \"block text-sm font-medium text-gray-700\",\n                                        children: \"Admin Address *\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 650,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        id: \"adminAddress\",\n                                        name: \"adminAddress\",\n                                        value: formData.adminAddress,\n                                        onChange: handleInputChange,\n                                        placeholder: \"0x...\",\n                                        className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 653,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                lineNumber: 649,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"tokenPrice\",\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Token Price\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                lineNumber: 667,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"number\",\n                                                step: \"0.01\",\n                                                id: \"tokenPrice\",\n                                                name: \"tokenPrice\",\n                                                value: formData.tokenPrice,\n                                                onChange: handleInputChange,\n                                                placeholder: \"1.00\",\n                                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                lineNumber: 670,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 666,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"currency\",\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Currency\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                lineNumber: 683,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                id: \"currency\",\n                                                name: \"currency\",\n                                                value: formData.currency,\n                                                onChange: handleInputChange,\n                                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"USD\",\n                                                        children: \"USD\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                        lineNumber: 693,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"EUR\",\n                                                        children: \"EUR\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                        lineNumber: 694,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"GBP\",\n                                                        children: \"GBP\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                        lineNumber: 695,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"JPY\",\n                                                        children: \"JPY\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                        lineNumber: 696,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"CAD\",\n                                                        children: \"CAD\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                        lineNumber: 697,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"AUD\",\n                                                        children: \"AUD\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                        lineNumber: 698,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"CHF\",\n                                                        children: \"CHF\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                        lineNumber: 699,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"CNY\",\n                                                        children: \"CNY\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                        lineNumber: 700,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"ETH\",\n                                                        children: \"ETH\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                        lineNumber: 701,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"BTC\",\n                                                        children: \"BTC\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                        lineNumber: 702,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"USDC\",\n                                                        children: \"USDC\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                        lineNumber: 703,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"USDT\",\n                                                        children: \"USDT\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                        lineNumber: 704,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                lineNumber: 686,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 682,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"tokenImageUrl\",\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Token Image URL\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                lineNumber: 709,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"url\",\n                                                id: \"tokenImageUrl\",\n                                                name: \"tokenImageUrl\",\n                                                value: formData.tokenImageUrl,\n                                                onChange: handleInputChange,\n                                                placeholder: \"https://...\",\n                                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                                lineNumber: 712,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 708,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                lineNumber: 665,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"bonusTiers\",\n                                        className: \"block text-sm font-medium text-gray-700\",\n                                        children: \"Bonus Tiers\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 725,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        id: \"bonusTiers\",\n                                        name: \"bonusTiers\",\n                                        value: formData.bonusTiers,\n                                        onChange: handleInputChange,\n                                        rows: 2,\n                                        placeholder: \"Early Bird: 10%, Standard: 5%, Late: 0%\",\n                                        className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 728,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                lineNumber: 724,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"tokenDetails\",\n                                        className: \"block text-sm font-medium text-gray-700\",\n                                        children: \"Token Details\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 740,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        id: \"tokenDetails\",\n                                        name: \"tokenDetails\",\n                                        value: formData.tokenDetails,\n                                        onChange: handleInputChange,\n                                        rows: 3,\n                                        placeholder: \"Describe your security token...\",\n                                        className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                        lineNumber: 743,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                lineNumber: 739,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pt-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: isSubmitting,\n                                    className: \"w-full bg-purple-600 hover:bg-purple-700 text-white font-bold py-3 px-4 rounded disabled:opacity-50\",\n                                    children: isSubmitting ? '🔄 Creating Modular Token...' : '🚀 Create Modular Token'\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                    lineNumber: 755,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                lineNumber: 754,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                        lineNumber: 580,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                lineNumber: 577,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6 p-4 bg-green-50 border border-green-200 rounded-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-shrink-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-green-600 text-xl\",\n                                children: \"✅\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                lineNumber: 771,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                            lineNumber: 770,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"ml-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-sm font-medium text-green-800\",\n                                    children: \"Production Ready\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                    lineNumber: 774,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-1 text-sm text-green-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"This interface deploys real modular security tokens using our factory contract. Each token is a new proxy instance with full upgrade capabilities.\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                            lineNumber: 778,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1\",\n                                            children: \"All tokens created here are fully functional and ready for production use on Amoy testnet.\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                            lineNumber: 779,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                                    lineNumber: 777,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                            lineNumber: 773,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                    lineNumber: 769,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n                lineNumber: 768,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-modular-token\\\\page.tsx\",\n        lineNumber: 427,\n        columnNumber: 5\n    }, this);\n}\n_s(CreateModularTokenPage, \"B48lQD7Qppt66Qd7SYrVcUCLgGM=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = CreateModularTokenPage;\nvar _c;\n$RefreshReg$(_c, \"CreateModularTokenPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/create-modular-token/page.tsx\n"));

/***/ })

});