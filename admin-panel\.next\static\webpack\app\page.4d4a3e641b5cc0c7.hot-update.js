"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/config.ts":
/*!***********************!*\
  !*** ./src/config.ts ***!
  \***********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   contractAddresses: () => (/* binding */ contractAddresses),\n/* harmony export */   defaultNetwork: () => (/* binding */ defaultNetwork),\n/* harmony export */   getContractAddresses: () => (/* binding */ getContractAddresses),\n/* harmony export */   getKnownTokens: () => (/* binding */ getKnownTokens),\n/* harmony export */   getNetworkConfig: () => (/* binding */ getNetworkConfig),\n/* harmony export */   isKnownToken: () => (/* binding */ isKnownToken),\n/* harmony export */   knownTokens: () => (/* binding */ knownTokens),\n/* harmony export */   networkConfig: () => (/* binding */ networkConfig),\n/* harmony export */   tokenTypes: () => (/* binding */ tokenTypes),\n/* harmony export */   verifyTokenExists: () => (/* binding */ verifyTokenExists)\n/* harmony export */ });\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n// Network configuration\nconst networkConfig = {\n    // Amoy testnet\n    amoy: {\n        chainId: 80002,\n        name: \"Amoy\",\n        rpcUrl: \"https://rpc-amoy.polygon.technology\",\n        blockExplorer: \"https://www.oklink.com/amoy\"\n    },\n    // Polygon mainnet\n    polygon: {\n        chainId: 137,\n        name: \"Polygon\",\n        rpcUrl: \"https://polygon-rpc.com\",\n        blockExplorer: \"https://polygonscan.com\"\n    }\n};\n// Default network\nconst defaultNetwork = \"amoy\";\n// Contract addresses - using the newly deployed factory address\nconst contractAddresses = {\n    // Updated to use ModularTokenFactory (working factory with deployToken function)\n    amoy: {\n        factory: \"0xa2CD33672A38C66722eb06cA1aB973889BB85b24\",\n        tokenImplementation: \"0xA4f70C1953165be63409e1F0Ff11c2D03522E8C2\",\n        whitelistImplementation: \"0x63eeE78ccc281413272bE68d9553Ae82680a0B09\",\n        whitelistWithKYCImplementation: \"0xf7c9C30Ad2E5b72F86489f26ab66cc1E79F44A7D\"\n    },\n    polygon: {\n        factory: process.env.NEXT_PUBLIC_FACTORY_ADDRESS_POLYGON || \"0x6543210987654321098765432109876543210987\"\n    }\n};\n// Known deployed tokens for fallback display (from memory)\nconst knownTokens = {\n    amoy: [\n        {\n            address: \"0x7544A3072FAA793e3f89048C31b794f171779544\",\n            name: \"Advanced Control Token\",\n            symbol: \"ACT\",\n            description: \"Security token with advanced transfer controls (conditional transfers, whitelisting, fees)\"\n        },\n        {\n            address: \"0xfccB88D208f5Ec7166ce2291138aaD5274C671dE\",\n            name: \"Augment_019\",\n            symbol: \"AUG019\",\n            description: \"Commodity token with 0 decimals, 1M max supply\"\n        },\n        {\n            address: \"0xe5F81d7dCeB8a8F97274C749773659B7288EcF90\",\n            name: \"Augment_01z\",\n            symbol: \"AUG01Z\",\n            description: \"Test token with custom configuration\"\n        },\n        {\n            address: \"0x391a0FA1498B869d0b9445596ed49b03aA8bf46e\",\n            name: \"Test Image Token\",\n            symbol: \"TIT2789\",\n            description: \"Test token with image URL support - deployed from upgraded factory\"\n        }\n    ]\n};\n// Token types for creating new tokens\nconst tokenTypes = [\n    {\n        id: \"equity\",\n        name: \"Equity\"\n    },\n    {\n        id: \"bond\",\n        name: \"Bond\"\n    },\n    {\n        id: \"debenture\",\n        name: \"Debenture\"\n    },\n    {\n        id: \"warrant\",\n        name: \"Warrant\"\n    },\n    {\n        id: \"realestate\",\n        name: \"Real Estate\"\n    },\n    {\n        id: \"carbon\",\n        name: \"Carbon Credit\"\n    },\n    {\n        id: \"commodity\",\n        name: \"Commodity\"\n    }\n];\n// Helper function to get contract addresses for the current network\nconst getContractAddresses = (network)=>{\n    return contractAddresses[network] || contractAddresses[defaultNetwork];\n};\n// Helper function to get network configuration for the current network\nconst getNetworkConfig = (network)=>{\n    return networkConfig[network] || networkConfig[defaultNetwork];\n};\n// Helper function to get known tokens for a network\nconst getKnownTokens = (network)=>{\n    return knownTokens[network] || [];\n};\n// Helper to check if a token exists in factory (in a real implementation, this would query the blockchain)\nconst verifyTokenExists = async (network, tokenAddress)=>{\n    // In a real implementation, this would:\n    // 1. Connect to the factory contract\n    // 2. Call a method or check events to verify the token's existence\n    // For demo purposes, we'll just return true\n    return true;\n};\n// Helper function to validate if an address is a known token\nconst isKnownToken = (network, tokenAddress)=>{\n    const tokens = getKnownTokens(network);\n    return tokens.some((token)=>token.address.toLowerCase() === tokenAddress.toLowerCase());\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/config.ts\n"));

/***/ })

});